"use client";

import { useState, useEffect } from "react";

export const useInAppBrowser = () => {
  const [isFacebookBrowser, setIsFacebookBrowser] = useState(false);
  const [isInAppBrowser, setIsInAppBrowser] = useState(false);

  useEffect(() => {
    const userAgent =
      navigator.userAgent || navigator.vendor || (window as any).opera;

    // Detect Facebook in-app browser (primary target)
    const isFb =
      userAgent.indexOf("FBAN") > -1 ||
      userAgent.indexOf("FBAV") > -1 ||
      userAgent.indexOf("FB_IAB") > -1 ||
      userAgent.indexOf("FBIOS") > -1;

    // Detect other problematic in-app browsers
    const isOtherInApp =
      userAgent.indexOf("Instagram") > -1 ||
      userAgent.indexOf("IGAB") > -1 ||
      userAgent.indexOf("Twitter") > -1 ||
      userAgent.indexOf("Line") > -1 ||
      userAgent.indexOf("WhatsApp") > -1 ||
      userAgent.indexOf("Messenger") > -1;

    setIsFacebookBrowser(isFb);
    setIsInAppBrowser(isFb || isOtherInApp);
  }, []);

  return {
    isFacebookBrowser,
    isInAppBrowser
  };
};
