"use server";

import { JWT } from "@udoy/libs/auth/jwt";
import { downloadImage, FileManager } from "@udoy/libs/backend/image-util";
import { ActionError } from "@udoy/utils/app-error";
import { CookieUtil } from "@udoy/utils/cookie-util";
import { getPrisma } from "@udoy/utils/db-utils";
import { getGoogleProfile } from "@udoy/utils/google";
import { cookies } from "next/headers";

export async function googleLogin(code?: string) {
  if (!code) {
    return null;
  }

  try {
    let cartId = await CookieUtil.cartId();
    let userId = await CookieUtil.userId();
    const prisma = getPrisma();

    if (userId) {
      let user = await prisma.user.findUnique({
        where: { id: userId },
        include: {
          cart: true,
          pushSubscriptions: true,
        },
      });

      if (user) {
        if (user.cart) {
          (await cookies()).set("cartId", user.cart.id);
        }

        return user;
      }
    }

    const profile = await getGoogleProfile(code);

    if (profile?.email) {
      let user = await getPrisma().user.findUnique({
        where: { email: profile.email },
        include: {
          cart: true,
          pushSubscriptions: true,
        },
      });

      if (!user) {
        let imageUrl = null;
        if (profile.picture) {
          const image = await downloadImage(profile.picture).catch((error) => {
            console.log(error);
            return null;
          });
          if (image) {
            imageUrl = await FileManager.imageUpload(image, "pictures").catch(
              (error) => {
                console.log(error);
                return null;
              }
            );
          }
        }

        const cart =
          cartId && (await prisma.cart.findUnique({ where: { id: cartId } }));
        let cartData = {
          create: {},
        };

        if (cart && cart.addressId === null) {
          cartData = {
            connect: {
              id: cartId,
            },
          } as any;
        }

        user = await getPrisma().user.create({
          data: {
            email: profile.email,
            name: profile.name || "UserName",
            avatar: imageUrl || profile.picture,
            cart: cartData,
          },

          include: {
            cart: true,
            pushSubscriptions: true,
          },
        });
      } else {
        const cart =
          cartId && (await prisma.cart.findUnique({ where: { id: cartId } }));

        if (cart && cart.userId === null && cart.id !== user.cart?.id) {
          await prisma.cartItem.deleteMany({
            where: { cartId: user.cart?.id! },
          });
          await prisma.cartItem.updateMany({
            where: { cartId },
            data: { cartId: user?.cart?.id! },
          });

          user = await prisma.user.findUnique({
            where: { id: user.id },
            include: {
              cart: true,
              pushSubscriptions: true,
            },
          });
        }
      }

      if (user) {
        // Set cookies to expire in 1 year (365 days)
        const oneYear = 60 * 60 * 24 * 365;

        (await cookies()).set("token", JWT.sign({ id: user.id }), {
          maxAge: oneYear,
        });
        (await cookies()).set("user", JSON.stringify(user), {
          maxAge: oneYear,
        });
        (await cookies()).set("cartId", user.cart?.id!, {
          maxAge: oneYear,
        });

        return user;
      }
    }
  } catch (error) {
    console.log(error);
    return ActionError("Failed to login");
  }

  return null;
}

export async function getUser() {
  const userId = await CookieUtil.userId();
  if (userId) {
    const prisma = getPrisma();
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        cart: {
          select: {
            items: true,
          },
        },
        pushSubscriptions: true,
      },
    });

    let count = 0;

    for (const item of user?.cart?.items || []) {
      count += item.quantity;
    }

    return { user, count };
  }

  return null;
}
