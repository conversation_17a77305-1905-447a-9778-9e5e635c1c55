"use server";

import { getPrisma } from "@udoy/utils/db-utils";
import { <PERSON>ieUtil } from "@udoy/utils/cookie-util";
import { ActionError } from "@udoy/utils/app-error";
import { Role, OrderStatus } from "@prisma/client";
import { revalidatePath } from "next/cache";

const STORE_EMAIL = "<EMAIL>";
const STORE_NAME = "Store Customer";
const STORE_PHONE = "***********";
const PICKUP_ZONE_SLUG = "pickup-point";

/**
 * Creates or gets the store user account for in-store sales
 */
async function getOrCreateStoreUser() {
  const prisma = getPrisma();
  
  // Try to find existing store user
  let storeUser = await prisma.user.findUnique({
    where: { email: STORE_EMAIL },
    include: {
      address: {
        include: { zone: true }
      },
      cart: true
    }
  });

  if (storeUser) {
    return storeUser;
  }

  // Get pickup point zone
  const pickupZone = await prisma.deliveryZone.findUnique({
    where: { slug: PICKUP_ZONE_SLUG }
  });

  if (!pickupZone) {
    throw new Error("Pickup point zone not found. Please contact administrator.");
  }

  // Create store user with address and cart
  storeUser = await prisma.user.create({
    data: {
      name: STORE_NAME,
      email: STORE_EMAIL,
      phone: STORE_PHONE,
      role: Role.USER,
      cart: {
        create: {}
      },
      address: {
        create: {
          label: "Store Pickup",
          name: STORE_NAME,
          home: "Udoy Mart Store",
          phone: STORE_PHONE,
          location: "In-store pickup",
          zoneId: pickupZone.id
        }
      }
    },
    include: {
      address: {
        include: { zone: true }
      },
      cart: true
    }
  });

  // Update cart to use the created address
  await prisma.cart.update({
    where: { id: storeUser?.cart?.id },
    data: { addressId: storeUser.address[0].id }
  });

  return storeUser;
}

/**
 * Creates an instant store sale order
 * This bypasses the normal order flow and creates a DELIVERED order immediately
 */
export async function createStoreSale() {
  try {
    const adminUserId = await CookieUtil.userId();
    
    if (!adminUserId) {
      return ActionError("Login to Continue");
    }

    const prisma = getPrisma();
    
    // Verify admin permissions
    const admin = await prisma.user.findUnique({
      where: {
        id: adminUserId,
        role: { in: [Role.ADMIN, Role.SUPER_ADMIN, Role.MAINTAINER] },
      },
    });

    if (!admin) {
      return ActionError("Unauthorized");
    }

    // Get admin's cart
    const adminCart = await prisma.cart.findUnique({
      where: { userId: adminUserId },
      include: {
        items: {
          include: {
            product: true
          }
        }
      }
    });

    if (!adminCart || adminCart.items.length === 0) {
      return ActionError("Cart is empty");
    }

    // Get or create store user
    const storeUser = await getOrCreateStoreUser();

    // Calculate order totals
    let subTotal = 0;
    let totalDiscount = 0;
    let totalProfit = 0;

    const orderItems = adminCart.items.map(item => {
      const originalPrice = item.product.price;
      const discountAmount = item.product.discount;
      const finalPrice = originalPrice - discountAmount;

      // Accumulate totals
      subTotal += originalPrice * item.quantity;
      totalDiscount += discountAmount * item.quantity;
      totalProfit += (finalPrice - item.product.sourcePrice) * item.quantity;

      return {
        price: finalPrice,
        quantity: item.quantity,
        productId: item.productId,
        sourcePrice: item.product.sourcePrice,
      };
    });

    // Create the order with DELIVERED status immediately
    const order = await prisma.order.create({
      data: {
        subTotal: subTotal - totalDiscount,
        discount: totalDiscount,
        shipping: 0, // No shipping for in-store pickup
        profit: totalProfit,
        buyerId: storeUser.id,
        addressId: storeUser.address[0].id,
        status: OrderStatus.DELIVERED,
        timeline: {
          createMany: {
            data: [
              {
                status: OrderStatus.PENDING,
                note: "Store Sale - Order Created",
              },
              {
                status: OrderStatus.CONFIRMED,
                note: "Store Sale - Auto Confirmed",
              },
              {
                status: OrderStatus.PACKED,
                note: "Store Sale - Auto Packed",
              },
              {
                status: OrderStatus.SHIPPING,
                note: "Store Sale - Auto Shipped",
              },
              {
                status: OrderStatus.DELIVERED,
                note: "Store Sale - Completed",
              }
            ]
          }
        },
        orderItems: {
          createMany: {
            data: orderItems.map(item => ({
              ...item,
              picked: true,
              pickedAt: new Date(),
              pickerId: adminUserId
            }))
          }
        }
      },
      include: {
        orderItems: {
          include: {
            product: true
          }
        },
        buyer: true,
        address: {
          include: {
            zone: true
          }
        }
      }
    });

    // Update product stock
    const stockUpdates = adminCart.items.map(item =>
      prisma.product.update({
        where: { id: item.productId },
        data: {
          supply: {
            decrement: item.quantity
          }
        }
      })
    );

    // Clear admin's cart
    const clearCart = prisma.cartItem.deleteMany({
      where: { cartId: adminCart.id }
    });

    // Execute all updates in transaction
    await prisma.$transaction([
      ...stockUpdates,
      clearCart
    ]);

    // Revalidate relevant pages
    revalidatePath("/dashboard/orders");
    revalidatePath("/dashboard");

    return {
      success: true,
      orderId: order.id,
      total: order.subTotal,
      discount: totalDiscount,
      message: `Store sale completed! Order #${order.id} for ৳${order.subTotal.toLocaleString()}${totalDiscount > 0 ? ` (৳${totalDiscount.toLocaleString()} discount applied)` : ''}`
    };

  } catch (error) {
    console.error("Store sale error:", error);
    return ActionError("Failed to complete store sale");
  }
}
