import { Fragment, ReactNode, Suspense } from "react";
import Header from "./components/Header";
import RecoilRoot from "@udoy/components/RecoilRoot";
import LoginPopup from "./components/LoginPopup";
import PWAInstallPrompt from "@udoy/components/PwaInstallPrompt";
import OpenInBrowserPrompt from "@udoy/components/InAppBrowserPrompt";

function ClientLayout({ children }: { children: ReactNode }) {
  return (
    <RecoilRoot>
      <Fragment>
        <Suspense fallback="Loading...">
          <Header />
        </Suspense>
        {children}
        <PWAInstallPrompt />
        <OpenInBrowserPrompt />
        <LoginPopup />
      </Fragment>
    </RecoilRoot>
  );
}

export default ClientLayout;
