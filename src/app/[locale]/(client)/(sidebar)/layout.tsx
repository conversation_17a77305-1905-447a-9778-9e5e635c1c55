import { ReactNode, Suspense } from "react";
import Sidebar from "../components/Sidebar";
import CartStatus from "../components/CartStatus";

function Layout({ children }: { children: ReactNode }) {
  return (
    <div className="flex ">
      <Suspense fallback={<div>Loading...</div>}>
        <Sidebar />
      </Suspense>
      <div className="flex-1">{children}</div>
      <CartStatus />
    </div>
  );
}

export default Layout;
