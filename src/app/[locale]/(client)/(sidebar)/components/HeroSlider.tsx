"use client";

import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@udoy/components/ui/carousel";
import Image from "next/image";

function HeroSlider() {
  return (
    <div className="mt-6">
      <Carousel
        className=" w-full max-w-6xl"
        opts={{ loop: true, dragFree: true }}
      >
        <CarouselContent>
          <CarouselItem>
            <Image
              src="/images/hero-1.jpg"
              width={1200}
              height={600}
              alt="cover"
              className="w-full rounded"
            />
          </CarouselItem>
          <CarouselItem>
            <Image
              src="/images/hero-2.jpg"
              width={1200}
              height={600}
              alt="cover"
              className="w-full rounded"
            />
          </CarouselItem>
          <CarouselItem>
            <Image
              src="/images/hero-3.jpg"
              width={1200}
              height={600}
              alt="cover"
              className="w-full rounded "
            />
          </CarouselItem>
        </CarouselContent>
        <div className="">
          <CarouselPrevious />
          <CarouselNext />
        </div>
      </Carousel>
    </div>
  );
}

export default HeroSlider;
