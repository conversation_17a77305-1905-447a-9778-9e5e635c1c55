"use client";

import Button from "@udoy/components/Button";
import Locale from "@udoy/components/Locale/Client";
import { useIsBangla } from "@udoy/hooks/useIsBangla";
import { store } from "@udoy/state";
import { useCart } from "@udoy/state/selectors";
import { useLocale } from "next-intl";
import React from "react";
import { addressPopupAtom } from "./ManageAddress";
import useStatus from "@udoy/hooks/useToastUtil";
import { withError } from "@udoy/utils/app-error";
import { placeOrder } from "@udoy/actions/cart";
import { useRouter } from "next/navigation";
import useInOperation from "@udoy/hooks/useInOperation";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@udoy/components/ui/alert-dialog";
import { BlockedUserDialog } from "./BlockedUserDialog";
import { Textarea } from "@udoy/components/ui/textarea";

function TotalPricing() {
  const { totalPrice, totalDiscount, deliveryFee } = useCart();
  const locale = useLocale();
  const isBangla = useIsBangla();
  const status = useStatus();
  const router = useRouter();
  const { isInOperation, message } = useInOperation();
  const [infoPopup, setInfoPopup] = React.useState(false);
  const [blockedUserDialog, setBlockedUserDialog] = React.useState(false);
  const [notes, setNotes] = React.useState("");

  const grandTotal = totalPrice + deliveryFee - totalDiscount;

  async function handlePlaceOrder() {
    try {
      status.loading("Confirming Your Order...");
      await withError(placeOrder(undefined, notes.trim() || undefined));

      status.success("Your Order Placed Successfully");
      router.replace("/order-successful");
    } catch (error: any) {
      // Check if the error is about blocked account
      if (error.message && error.message.includes("blocked")) {
        setBlockedUserDialog(true);
        status.error(error.message);
      } else {
        status.error(error.message || "Failed To Place Order");
      }
    }
  }

  async function handleConfirm() {
    const state = store.getSnapshot();

    if (!Boolean(state.context.me)) {
      status.error(
        isBangla ? "অনুগ্রহ করে লগইন করুন" : "Please Login To Continue"
      );

      return store.send({ type: "setLoginPopup", open: true });
    }

    if (state.context.addresses.length === 0) {
      status.error(
        isBangla
          ? "অনুগ্রহ করে একটি ডেলিভারি ঠিকানা যোগ করুন!"
          : "Please add a delivery address."
      );

      return addressPopupAtom.set(true);
    }

    if (!isInOperation) {
      return setInfoPopup(true);
    }

    await handlePlaceOrder();
  }

  return (
    <div className="shadow rounded text-sm overflow-clip border">
      <AlertDialog open={infoPopup} onOpenChange={setInfoPopup}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="text-left">
              {message?.title}
            </AlertDialogTitle>
            <AlertDialogDescription className="text-left">
              {message?.description}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>
              <Locale bn="বাতিল করুন">Cancel</Locale>
            </AlertDialogCancel>
            <AlertDialogAction onClick={handlePlaceOrder}>
              <Locale bn="অর্ডার করুন">Place Order</Locale>
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      <div className="p-4">
        <div className="flex justify-between">
          <span>
            <Locale bn="সাব-টোটাল">Subtotal</Locale>:
          </span>
          <span>৳{totalPrice.toLocaleString(locale)}</span>
        </div>
        <div className="flex justify-between mt-2">
          <span>
            <Locale bn="ডিসকাউন্ট">Discount</Locale>:
          </span>
          <span>
            {totalDiscount ? "-" : ""}৳{totalDiscount.toLocaleString(locale)}
          </span>
        </div>
        <div className="flex justify-between">
          <span>
            <Locale bn="ডেলিভারি ফি">Delivery Fee</Locale>:
          </span>
          <span>৳{deliveryFee.toLocaleString(locale)}</span>
        </div>

        <div className="mt-4">
          <label className="block text-sm font-medium mb-2">
            <Locale bn="অতিরিক্ত নোট (ঐচ্ছিক)">Additional Notes (Optional)</Locale>:
          </label>
          <Textarea
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            placeholder={isBangla ? "অর্ডার সম্পর্কে কোনো বিশেষ নির্দেশনা..." : "Any special instructions for your order..."}
            className="w-full text-sm"
            rows={3}
            maxLength={500}
          />
        </div>

        <div className="flex justify-between mt-4">
          <span>
            <Locale bn="মোট">Total</Locale>:
          </span>
          <span>৳{grandTotal.toLocaleString(locale)}</span>
        </div>
      </div>

      <div className="grid grid-cols-2 mt-3 sm:static fixed bottom-0 w-full left-0">
        <span className="bg-brand grid place-items-center">
          <Locale bn="মোট">Total</Locale>: ৳{grandTotal.toLocaleString(locale)}
        </span>
        <Button className="rounded-none h-12 sm:h-10 " onClick={handleConfirm}>
          <Locale bn="নিশ্চিত করুন">Confirm</Locale>
        </Button>
      </div>

      {/* Blocked User Dialog */}
      <BlockedUserDialog
        isOpen={blockedUserDialog}
        onClose={() => setBlockedUserDialog(false)}
      />
    </div>
  );
}

export default TotalPricing;
