import type { Metada<PERSON> } from "next";
import { HomeIcon } from "lucide-react";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from "@udoy/components/ui/breadcrumb";
import { getPrisma } from "@udoy/utils/db-utils";
import { CookieUtil } from "@udoy/utils/cookie-util";
import RenderOrders from "./components/SyncOrders";
import { OrdersPagination } from "../../dashboard/orders/components/OrdersPagination";

export const metadata: Metadata = {
  title: "Orders | User Dashboard",
  description: "View and manage your order history",
};

interface SearchParams {
  page?: string;
  limit?: string;
}

interface PageProps {
  searchParams: Promise<SearchParams>;
}

async function getData(page: number = 1, limit: number = 10) {
  const prisma = getPrisma();
  const userId = await CookieUtil.userId();

  if (!userId) {
    return { orders: [], totalCount: 0 };
  }

  const skip = (page - 1) * limit;

  const [orders, totalCount] = await Promise.all([
    prisma.order.findMany({
      where: {
        buyerId: userId,
      },
      include: {
        orderItems: {
          include: {
            product: {
              include: {
                images: true,
              },
            },
          },
        },
        address: true,
        buyer: true,
      },
      orderBy: {
        createdAt: 'desc', // Latest first
      },
      skip,
      take: limit,
    }),
    prisma.order.count({
      where: {
        buyerId: userId,
      },
    }),
  ]);

  return { orders, totalCount };
}

export default async function OrdersPage({ searchParams }: PageProps) {
  const params = await searchParams;
  const page = parseInt(params.page || '1');
  const limit = parseInt(params.limit || '10');

  const { orders, totalCount } = await getData(page, limit);

  // Calculate pagination info
  const totalPages = Math.ceil(totalCount / limit);
  const currentPage = page;
  const hasNextPage = currentPage < totalPages;
  const hasPrevPage = currentPage > 1;

  const paginationInfo = {
    totalCount,
    totalPages,
    currentPage,
    hasNextPage,
    hasPrevPage,
    limit,
  };
  return (
    <div className="container mx-auto py-6 px-4 md:px-6">
      <div className="flex flex-col gap-6">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink className="flex" href="/">
                <HomeIcon className="h-4 w-4 mr-1" />
                Home
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href="/orders">My orders</BreadcrumbLink>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <h1 className="text-3xl font-bold tracking-tight">My orders</h1>
          {/* <div className="flex items-center gap-2 w-full md:w-auto">
            <div className="relative w-full md:w-[300px]">
              <SearchIcon className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search orders..."
                className="pl-8 w-full"
              />
            </div>
          </div> */}
        </div>

        {/* <OrderFilters /> */}

        <RenderOrders orders={orders} />

        {totalCount > 0 && <OrdersPagination paginationInfo={paginationInfo} />}
      </div>
    </div>
  );
}
