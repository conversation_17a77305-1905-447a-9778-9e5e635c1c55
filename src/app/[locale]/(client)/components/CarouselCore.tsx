import React from "react";
import { cn } from "@udoy/utils/shadcn";
import Autoplay from "embla-carousel-autoplay";
import { CategoryWithSubcategories } from "@udoy/utils/types";
import Locale from "@udoy/components/Locale";
import Link from "next/link";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@udoy/components/ui/carousel";
import ProductItem from "./ProductItem";
import { Category } from "@prisma/client";
import { ProductWithRelations } from "./CategoryProductCarousel";

interface CarouselCoreProps {
  category: Category | null;
  products: ProductWithRelations[];
  className?: string;
  showNavigation?: boolean;
  autoplay?: boolean;
  delay?: number;
  locale: string;
}

function CarouselCore({
  category,
  products,
  className = "",
  showNavigation = true,
  autoplay = false,
  delay = 2000,
  locale,
}: CarouselCoreProps) {
  const responsiveClasses =
    "pl-2 md:pl-4 basis-1/2 sm:basis-1/3 md:basis-1/4 lg:basis-1/5 xl:basis-1/6 min-h-[320px]";
  return (
    <div className={cn("mt-8 w-full h-full", className)}>
      <h3 className="text-2xl font-bold text-center mb-6">
        <Link href={{ pathname: `/${category?.slug}` }}>
          <Locale bn={category?.nam}>{category?.name}</Locale>
        </Link>
      </h3>

      <div className="xl:px-6">
        <Carousel
          className="h-full"
          delay={delay}
          opts={{
            align: "start",
            loop: autoplay,
            dragFree: true,
            skipSnaps: true,
          }}
        >
          <CarouselContent className="-ml-2 md:-ml-4">
            {products.map((product) => (
              <CarouselItem
                key={product.id}
                className={cn(responsiveClasses, "h-full min-h-full")}
              >
                <ProductItem product={product} locale={locale} singleLine />
              </CarouselItem>
            ))}
          </CarouselContent>
        </Carousel>
      </div>
    </div>
  );
}

export default CarouselCore;
