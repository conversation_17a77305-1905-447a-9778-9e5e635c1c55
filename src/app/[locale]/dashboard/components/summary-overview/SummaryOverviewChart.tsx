"use client";

import {
  <PERSON><PERSON><PERSON>ive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>lt<PERSON>,
  Legend,
  CartesianGrid,
} from "recharts";

export interface SummaryChartData {
  name: string;
  revenue: number;
  profit: number;
  orders: number;
  period: string;
}

interface SummaryOverviewChartProps {
  data: SummaryChartData[];
}

export function SummaryOverviewChart({ data }: SummaryOverviewChartProps) {

  return (
    <ResponsiveContainer width="100%" height={350}>
      <BarChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
        <XAxis
          dataKey="name"
          stroke="#888888"
          fontSize={12}
          tickLine={false}
          axisLine={false}
        />
        <YAxis
          stroke="#888888"
          fontSize={12}
          tickLine={false}
          axisLine={false}
          tickFormatter={(value) => `৳ ${value.toLocaleString()}`}
        />
        <Tooltip
          formatter={(value, name) => {
            if (name === "orders") {
              return [`${value} orders`, "Orders"];
            }
            return [`৳ ${Number(value).toLocaleString()}`, name === "revenue" ? "Revenue" : "Profit"];
          }}
          labelFormatter={(label) => `Day: ${label}`}
          wrapperClassName="rounded border bg-background shadow-md"
        />
        <Legend />
        <Bar
          dataKey="revenue"
          name="Revenue"
          fill="#3b82f6"
          radius={[2, 2, 0, 0]}
        />
        <Bar
          dataKey="profit"
          name="Profit"
          fill="#10b981"
          radius={[2, 2, 0, 0]}
        />
      </BarChart>
    </ResponsiveContainer>
  );
}
