import { Suspense } from "react";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@udoy/components/ui/card";
import { SummaryOverviewChart } from "./SummaryOverviewChart";
import { getDailySummaryData } from "./useSummaryData";
import {
  unstable_cacheTag as cacheTag,
  unstable_cacheLife as cacheLife,
} from "next/cache";
import { CacheKey } from "@udoy/utils/cache-key";

async function SummaryOverviewContent() {
  "use cache";
  cacheTag(CacheKey.DashboardStats("summary-data-daily"));
  cacheLife("hours");

  const data = await getDailySummaryData();

  return (
    <Card className="col-span-4">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Summary Overview</CardTitle>
            <CardDescription>
              Daily business performance based on summary data (Last 30 days)
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pl-2">
        {data.length === 0 ? (
          <div className="flex items-center justify-center h-[350px]">
            <div className="text-muted-foreground">
              No summary data available for daily view
            </div>
          </div>
        ) : (
          <SummaryOverviewChart data={data} />
        )}
      </CardContent>
    </Card>
  );
}

function SummaryOverviewLoading() {
  return (
    <Card className="col-span-4">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Summary Overview</CardTitle>
            <CardDescription>
              Daily business performance based on summary data (Last 30 days)
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pl-2">
        <div className="flex items-center justify-center h-[350px]">
          <div className="text-muted-foreground">Loading summary data...</div>
        </div>
      </CardContent>
    </Card>
  );
}

export function SummaryOverview() {
  return (
    <Suspense fallback={<SummaryOverviewLoading />}>
      <SummaryOverviewContent />
    </Suspense>
  );
}
