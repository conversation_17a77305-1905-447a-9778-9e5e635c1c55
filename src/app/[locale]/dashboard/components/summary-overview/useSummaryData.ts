import { getPrisma } from "@udoy/utils/db-utils";
import { SummaryType } from "@prisma/client";
import {
  startOfDay,
  endOfDay,
  subDays,
  format
} from "date-fns";
import { SummaryChartData } from "./SummaryOverviewChart";

/**
 * Gets daily summary data for the last 30 days
 * Note: This function assumes authentication is handled at the component level
 */
export async function getDailySummaryData(): Promise<SummaryChartData[]> {
  const prisma = getPrisma();

  const summaryType = SummaryType.DAILY;
  const periods: { start: Date; end: Date; name: string }[] = [];
  const now = new Date();

  // Get last 30 days
  for (let i = 29; i >= 0; i--) {
    const date = subDays(now, i);
    periods.push({
      start: startOfDay(date),
      end: endOfDay(date),
      name: format(date, "MMM d"),
    });
  }

  // Fetch summaries for each period
  const data = await Promise.all(
    periods.map(async (period) => {
      const summaries = await prisma.summary.findMany({
        where: {
          type: summaryType,
          startDate: {
            gte: period.start,
            lte: period.end,
          },
        },
      });

      // Aggregate data for the period
      const totalRevenue = summaries.reduce((sum, s) => sum + s.totalRevenue, 0);
      const totalProfit = summaries.reduce((sum, s) => sum + s.totalProfit, 0);
      const totalOrders = summaries.reduce((sum, s) => sum + s.totalOrders, 0);

      return {
        name: period.name,
        revenue: totalRevenue,
        profit: totalProfit,
        orders: totalOrders,
        period: period.name,
      };
    })
  );

  return data;
}
