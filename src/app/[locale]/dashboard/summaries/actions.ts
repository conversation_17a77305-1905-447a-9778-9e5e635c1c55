"use server";

import { getPrisma } from "@udoy/utils/db-utils";
import { CookieUtil } from "@udoy/utils/cookie-util";
import { Summary, SummaryType, Order, OrderStatus, Role } from "@prisma/client";
import { revalidatePath } from "next/cache";
import { ActionError } from "@udoy/utils/app-error";
import {
  sendDiscordSummaryMessage,
  type DiscordSummaryMessage,
} from "@udoy/libs/backend/discord";

// Type definitions for return values
export type SummaryWithCreator = Summary & {
  generatedBy: {
    id: number;
    name: string;
    email: string;
  };
};

export type OrderWithDetails = Order & {
  buyer: {
    id: number;
    name: string;
    email: string;
  };
  orderItems: Array<{
    id: number;
    productId: string;
    quantity: number;
    price: number;
    sourcePrice: number;
    product: {
      id: string;
      name: string;
      sourcePrice: number;
    };
  }>;
};

export type SummaryWithDetails = Summary & {
  generatedBy: {
    id: number;
    name: string;
    email: string;
  };
  orders: OrderWithDetails[];
  childSummaries: SummaryWithCreator[];
};

/**
 * Fetch all summaries with creator information
 */
export async function getSummaries(type?: SummaryType) {
  try {
    const userId = await CookieUtil.userId();
    if (!userId) {
      return ActionError("Login to Continue");
    }

    const prisma = getPrisma();

    const admin = await prisma.user.findUnique({
      where: {
        id: userId,
        role: {
          in: [Role.ADMIN, Role.SUPER_ADMIN, Role.MAINTAINER],
        },
      },
    });

    if (!admin) {
      return ActionError("Unauthorized");
    }

    const whereClause = type ? { type } : {};

    const summaries = await prisma.summary.findMany({
      where: whereClause,
      include: {
        generatedBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return summaries;
  } catch (error) {
    console.error("Failed to fetch summaries:", error);
    return ActionError("Failed to fetch summaries");
  }
}

/**
 * Fetch orders within a specific date range for summary creation
 * Now filters by delivery date instead of creation date
 */
export async function getOrdersForDateRange(startDate: Date, endDate: Date) {
  try {
    const userId = await CookieUtil.userId();
    if (!userId) {
      return ActionError("Login to Continue");
    }

    const prisma = getPrisma();

    const admin = await prisma.user.findUnique({
      where: {
        id: userId,
        role: {
          in: [Role.ADMIN, Role.SUPER_ADMIN, Role.MAINTAINER],
        },
      },
    });

    if (!admin) {
      return ActionError("Unauthorized");
    }

    // Find orders that were delivered within the specified date range
    const orders = await prisma.order.findMany({
      where: {
        status: OrderStatus.DELIVERED,
        timeline: {
          some: {
            status: OrderStatus.DELIVERED,
            createdAt: {
              gte: startDate,
              lte: endDate,
            },
          },
        },
      },
      include: {
        buyer: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        orderItems: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                sourcePrice: true,
              },
            },
          },
        },
        timeline: {
          where: {
            status: OrderStatus.DELIVERED,
          },
          orderBy: {
            createdAt: "desc",
          },
          take: 1,
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Calculate profit for each order if not already set
    // const ordersWithProfit = orders.map(order => {
    //   if (order.profit === 0) {
    //     const calculatedProfit = order.orderItems.reduce((total, item) => {
    //       const itemProfit = (item.price - item.sourcePrice) * item.quantity;
    //       return total + itemProfit;
    //     }, 0);
    //     return { ...order, profit: calculatedProfit };
    //   }
    //   return order;
    // });

    return orders;
  } catch (error) {
    console.error("Failed to fetch orders for date range:", error);
    return ActionError("Failed to fetch orders for the specified date range");
  }
}

/**
 * Fetch child summaries for a given date range (used for weekly/monthly/yearly summaries)
 */
export async function getChildSummariesForDateRange(
  type: SummaryType,
  startDate: Date,
  endDate: Date
) {
  try {
    const userId = await CookieUtil.userId();
    if (!userId) {
      return ActionError("Login to Continue");
    }

    const prisma = getPrisma();

    const admin = await prisma.user.findUnique({
      where: {
        id: userId,
        role: {
          in: [Role.ADMIN, Role.SUPER_ADMIN, Role.MAINTAINER],
        },
      },
    });

    if (!admin) {
      return ActionError("Unauthorized");
    }

    // Determine the child summary type based on parent type
    let childType: SummaryType;
    switch (type) {
      case SummaryType.WEEKLY:
        return ActionError("Weekly creation is disabled");
      case SummaryType.MONTHLY:
        childType = SummaryType.DAILY;
        break;
      case SummaryType.YEARLY:
        childType = SummaryType.MONTHLY;
        break;
      default:
        return []; // Daily summaries don't have child summaries
    }

    const childSummaries = await prisma.summary.findMany({
      where: {
        type: childType,
        startDate: {
          gte: startDate,
        },
        endDate: {
          lte: endDate,
        },
        isFinalized: true,
      },
      include: {
        generatedBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        startDate: "asc",
      },
    });

    return childSummaries;
  } catch (error) {
    console.error("Failed to fetch child summaries:", error);
    return ActionError(
      "Failed to fetch child summaries for the specified date range"
    );
  }
}

/**
 * Create a new summary with the provided data
 */
export async function createSummary(data: {
  type: SummaryType;
  startDate: Date;
  endDate: Date;
  totalOrders: number;
  totalRevenue: number;
  totalDeliveryCharges: number;
  totalProfit: number;
  notes?: string | null;
  orderIds?: number[];
}) {
  try {
    const userId = await CookieUtil.userId();
    if (!userId) {
      return ActionError("Login to Continue");
    }

    const prisma = getPrisma();

    const admin = await prisma.user.findUnique({
      where: {
        id: userId,
        role: {
          in: [Role.ADMIN, Role.SUPER_ADMIN, Role.MAINTAINER],
        },
      },
    });

    if (!admin) {
      return ActionError("Unauthorized");
    }

    // Check if a summary already exists for this type and date range
    const existingSummary = await prisma.summary.findFirst({
      where: {
        type: data.type,
        startDate: data.startDate,
        endDate: data.endDate,
      },
    });

    if (existingSummary) {
      return ActionError(
        `A ${data.type.toLowerCase()} summary already exists for this date range`
      );
    }

    // For non-daily summaries, find and link child summaries
    if (data.type !== SummaryType.DAILY) {
      // Find existing child summaries that fall within this date range
      let childType: SummaryType;
      switch (data.type) {
        case SummaryType.WEEKLY:
          childType = SummaryType.DAILY;
          break;
        case SummaryType.MONTHLY:
          childType = SummaryType.WEEKLY;
          break;
        case SummaryType.YEARLY:
          childType = SummaryType.MONTHLY;
          break;
        default:
          childType = SummaryType.DAILY;
      }

      // Update child summaries to link to this parent
      await prisma.summary.updateMany({
        where: {
          type: childType,
          startDate: {
            gte: data.startDate,
          },
          endDate: {
            lte: data.endDate,
          },
          isFinalized: true,
          parentId: null, // Only link summaries that don't already have a parent
        },
        data: {
          parentId: null, // We'll set this after creating the summary
        },
      });
    }

    // Create the summary
    const summary = await prisma.summary.create({
      data: {
        type: data.type,
        startDate: data.startDate,
        endDate: data.endDate,
        totalOrders: data.totalOrders,
        totalRevenue: data.totalRevenue,
        totalDeliveryCharges: data.totalDeliveryCharges,
        totalProfit: data.totalProfit,
        notes: data.notes,
        isFinalized: true,
        generatedById: userId,
      },
      include: {
        generatedBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    // Now update child summaries to link to this parent
    if (data.type !== SummaryType.DAILY) {
      let childType: SummaryType;
      switch (data.type) {
        case SummaryType.WEEKLY:
          childType = SummaryType.DAILY;
          break;
        case SummaryType.MONTHLY:
          childType = SummaryType.DAILY;
          break;
        case SummaryType.YEARLY:
          childType = SummaryType.MONTHLY;
          break;
        default:
          childType = SummaryType.DAILY;
      }

      await prisma.summary.updateMany({
        where: {
          type: childType,
          startDate: {
            gte: data.startDate,
          },
          endDate: {
            lte: data.endDate,
          },
          isFinalized: true,
          parentId: null,
        },
        data: {
          parentId: summary.id,
        },
      });
    }

    // If order IDs are provided, update those orders to link to this summary
    if (data.orderIds && data.orderIds.length > 0) {
      await prisma.order.updateMany({
        where: {
          id: {
            in: data.orderIds,
          },
        },
        data: {
          summaryId: summary.id,
        },
      });
    }

    // Send Discord notification
    try {
      const discordMessage: DiscordSummaryMessage = {
        id: summary.id,
        type: summary.type,
        startDate: summary.startDate,
        endDate: summary.endDate,
        totalOrders: summary.totalOrders,
        totalRevenue: summary.totalRevenue,
        totalProfit: summary.totalProfit,
        totalDeliveryCharges: summary.totalDeliveryCharges,
        generatedBy: {
          name: summary.generatedBy.name,
          email: summary.generatedBy.email,
        },
      };

      // Send Discord notification (don't await to avoid blocking the response)
      sendDiscordSummaryMessage(discordMessage);
    } catch (error) {
      console.error("Failed to send Discord notification:", error);
      // Don't fail the summary creation if Discord notification fails
    }

    // Revalidate the summaries page
    revalidatePath("/dashboard/summaries");

    return summary;
  } catch (error) {
    console.error("Failed to create summary:", error);
    return ActionError("Failed to create summary");
  }
}

/**
 * Get a specific summary by ID with all related data
 */
export async function getSummaryById(id: string) {
  try {
    const userId = await CookieUtil.userId();
    if (!userId) {
      return ActionError("Login to Continue");
    }

    const prisma = getPrisma();

    const admin = await prisma.user.findUnique({
      where: {
        id: userId,
        role: {
          in: [Role.ADMIN, Role.SUPER_ADMIN, Role.MAINTAINER],
        },
      },
    });

    if (!admin) {
      return ActionError("Unauthorized");
    }

    const summary = await prisma.summary.findUnique({
      where: { id },
      include: {
        generatedBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        orders: {
          include: {
            buyer: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            orderItems: {
              include: {
                product: {
                  select: {
                    id: true,
                    name: true,
                    sourcePrice: true,
                  },
                },
              },
            },
          },
        },
        childSummaries: {
          include: {
            generatedBy: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
          orderBy: {
            startDate: "asc",
          },
        },
      },
    });

    return summary;
  } catch (error) {
    console.error("Failed to fetch summary by ID:", error);
    return ActionError("Failed to fetch summary details");
  }
}

/**
 * Update order profit in the database
 */
export async function updateOrderProfit(orderId: number, newProfit: number) {
  try {
    const userId = await CookieUtil.userId();
    if (!userId) {
      return ActionError("Login to Continue");
    }

    const prisma = getPrisma();

    const admin = await prisma.user.findUnique({
      where: {
        id: userId,
        role: {
          in: [Role.ADMIN, Role.SUPER_ADMIN, Role.MAINTAINER],
        },
      },
    });

    if (!admin) {
      return ActionError("Unauthorized");
    }

    // Validate the new profit value (allow negative profits)
    if (isNaN(newProfit)) {
      return ActionError("Invalid profit value");
    }

    // Update the order profit
    const updatedOrder = await prisma.order.update({
      where: { id: orderId },
      data: { profit: newProfit },
      include: {
        buyer: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        orderItems: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                sourcePrice: true,
              },
            },
          },
        },
      },
    });

    // Revalidate the summaries page
    revalidatePath("/dashboard/summaries");
    // Revalidate deliver section to reflect profit changes
    revalidatePath("/deliver");

    return updatedOrder;
  } catch (error) {
    console.error("Failed to update order profit:", error);
    return ActionError("Failed to update order profit");
  }
}

/**
 * Delete a summary (only for super admin or creator)
 */
export async function deleteSummary(summaryId: string) {
  try {
    const userId = await CookieUtil.userId();
    if (!userId) {
      return ActionError("Login to Continue");
    }

    const prisma = getPrisma();

    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      return ActionError("User not found");
    }

    // Get the summary to check ownership
    const summary = await prisma.summary.findUnique({
      where: { id: summaryId },
      include: {
        generatedBy: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    if (!summary) {
      return ActionError("Summary not found");
    }

    // Check if user is super admin or the creator of the summary
    const isSuperAdmin = user.role === Role.SUPER_ADMIN;
    const isCreator = summary.generatedById === userId;

    if (!isSuperAdmin && !isCreator) {
      return ActionError(
        "You can only delete summaries you created or be a super admin"
      );
    }

    // Delete the summary (this will also unlink related orders due to foreign key constraints)
    await prisma.summary.delete({
      where: { id: summaryId },
    });

    // Revalidate the summaries page
    revalidatePath("/dashboard/summaries");

    return { success: true, message: "Summary deleted successfully" };
  } catch (error) {
    console.error("Failed to delete summary:", error);
    return ActionError("Failed to delete summary");
  }
}

/**
 * Calculate profit for orders (helper function)
 */
export async function calculateOrderProfit(
  order: OrderWithDetails
): Promise<number> {
  return order.orderItems.reduce((total, item) => {
    const itemProfit = (item.price - item.sourcePrice) * item.quantity;
    return total + itemProfit;
  }, 0);
}
