"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from "@udoy/components/ui/dialog";
import { Button } from "@udoy/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@udoy/components/ui/card";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@udoy/components/ui/table";
import { Checkbox } from "@udoy/components/ui/checkbox";
import { Textarea } from "@udoy/components/ui/textarea";
import { Badge } from "@udoy/components/ui/badge";
import { CalendarIcon, Loader2 } from "lucide-react";
import {
  format,
  startOfWeek,
  endOfWeek,
  startOfMonth,
  endOfMonth,
  startOfYear,
  endOfYear,
} from "date-fns";
import {
  getOrdersForDateRange,
  getChildSummariesForDateRange,
  createSummary,
  updateOrderProfit,
  type SummaryWithCreator,
  type OrderWithDetails,
} from "../actions";
import { SummaryType } from "@prisma/client";
import { cn } from "@udoy/utils/shadcn";
import { toast } from "sonner";
import { Input } from "@udoy/components/ui/input";
import { Label } from "@udoy/components/ui/label";
import { withError } from "@udoy/utils/app-error";
import Link from "next/link";

interface SummaryCreationModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSummaryCreated: () => void;
}

type Order = OrderWithDetails;

export function SummaryCreationModal({
  open,
  onOpenChange,
  onSummaryCreated,
}: SummaryCreationModalProps) {
  const [step, setStep] = useState(1);
  const [selectedType, setSelectedType] = useState<SummaryType | null>(null);
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined);
  const [loading, setLoading] = useState(false);
  const [orders, setOrders] = useState<Order[]>([]);
  const [childSummaries, setChildSummaries] = useState<SummaryWithCreator[]>(
    []
  );
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [notes, setNotes] = useState("");
  const [editingOrder, setEditingOrder] = useState<Order | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  const summaryTypes = [
    {
      type: SummaryType.DAILY,
      label: "Daily",
      description: "Generate summary for a single day",
    },
    // {
    //   type: SummaryType.WEEKLY,
    //   label: "Weekly",
    //   description: "Generate summary for a week",
    // },
    {
      type: SummaryType.MONTHLY,
      label: "Monthly",
      description: "Generate summary for a month",
    },
    {
      type: SummaryType.YEARLY,
      label: "Yearly",
      description: "Generate summary for a year",
    },
  ];

  const resetModal = () => {
    setStep(1);
    setSelectedType(null);
    setSelectedDate(undefined);
    setOrders([]);
    setChildSummaries([]);
    setSelectedItems(new Set());
    setNotes("");
    setEditingOrder(null);
    setIsEditModalOpen(false);
  };

  const handleClose = () => {
    resetModal();
    onOpenChange(false);
  };

  const getDateRange = (type: SummaryType, date: Date) => {
    switch (type) {
      case SummaryType.DAILY:
        return {
          start: new Date(date.getFullYear(), date.getMonth(), date.getDate()),
          end: new Date(
            date.getFullYear(),
            date.getMonth(),
            date.getDate(),
            23,
            59,
            59
          ),
        };
      case SummaryType.WEEKLY:
        return {
          start: startOfWeek(date, { weekStartsOn: 0 }),
          end: endOfWeek(date, { weekStartsOn: 0 }),
        };
      case SummaryType.MONTHLY:
        return {
          start: startOfMonth(date),
          end: endOfMonth(date),
        };
      case SummaryType.YEARLY:
        return {
          start: startOfYear(date),
          end: endOfYear(date),
        };
      default:
        return { start: date, end: date };
    }
  };

  const fetchData = async () => {
    if (!selectedType || !selectedDate) return;

    setLoading(true);
    try {
      const { start, end } = getDateRange(selectedType, selectedDate);

      if (selectedType === SummaryType.DAILY) {
        const fetchedOrders = await withError(
          getOrdersForDateRange(start, end)
        );
        setOrders(fetchedOrders);
        if (fetchedOrders.length === 0) {
          toast.info("No orders found for the selected date.");
        }
      } else {
        const fetchedSummaries = await withError(
          getChildSummariesForDateRange(selectedType, start, end)
        );
        setChildSummaries(fetchedSummaries);
        if (fetchedSummaries.length === 0) {
          toast.info(
            `No ${
              selectedType === SummaryType.WEEKLY
                ? "daily"
                : selectedType === SummaryType.MONTHLY
                ? "weekly"
                : "monthly"
            } summaries found for the selected period.`
          );
        }
      }

      setStep(2);
    } catch (error: any) {
      console.error("Failed to fetch data:", error);
      toast.error(error?.message || "Failed to fetch data. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (selectedType === SummaryType.DAILY) {
      if (checked) {
        setSelectedItems(new Set(orders.map((order) => order.id.toString())));
      } else {
        setSelectedItems(new Set());
      }
    } else {
      if (checked) {
        setSelectedItems(new Set(childSummaries.map((summary) => summary.id)));
      } else {
        setSelectedItems(new Set());
      }
    }
  };

  const handleItemSelect = (id: string, checked: boolean) => {
    const newSelected = new Set(selectedItems);
    if (checked) {
      newSelected.add(id);
    } else {
      newSelected.delete(id);
    }
    setSelectedItems(newSelected);
  };

  const handleEditOrder = (order: Order) => {
    setEditingOrder(order);
    setIsEditModalOpen(true);
  };

  const handleSaveEditedOrder = async (updatedOrder: Order) => {
    try {
      // Update the order profit in the database
      const savedOrder = await withError(
        updateOrderProfit(updatedOrder.id, updatedOrder.profit)
      );

      // Update the local state with the saved order
      setOrders((prev) =>
        prev.map((order) =>
          order.id === updatedOrder.id
            ? { ...order, profit: savedOrder.profit }
            : order
        )
      );

      toast.success("Order profit updated successfully!");
      setIsEditModalOpen(false);
    } catch (error: any) {
      console.error("Failed to update order profit:", error);
      toast.error(
        error?.message || "Failed to update order profit. Please try again."
      );
    }
  };

  // Calculate totals for selected items
  const calculateTotals = () => {
    if (selectedType === SummaryType.DAILY) {
      const selectedOrders = orders.filter((order) =>
        selectedItems.has(order.id.toString())
      );
      const totalProfit = selectedOrders.reduce(
        (acc, order) => acc + order.profit,
        0
      );
      const totalDeliveryCharges = selectedOrders.reduce(
        (acc, order) => acc + order.shipping,
        0
      );
      const totalRevenue = selectedOrders.reduce(
        (acc, order) => acc + order.subTotal,
        0
      );
      // Calculate total source price from order items
      const totalSourcePrice = selectedOrders.reduce(
        (acc, order) => {
          const orderSourcePrice = order.orderItems.reduce(
            (itemAcc, item) => itemAcc + (item.sourcePrice * item.quantity),
            0
          );
          return acc + orderSourcePrice;
        },
        0
      );
      return {
        totalProfit,
        totalDeliveryCharges,
        totalRevenue,
        totalSourcePrice,
        combined: totalProfit + totalDeliveryCharges,
      };
    } else {
      const selectedSummaries = childSummaries.filter((summary) =>
        selectedItems.has(summary.id)
      );
      const totalProfit = selectedSummaries.reduce(
        (acc, summary) => acc + Number(summary.totalProfit),
        0
      );
      const totalDeliveryCharges = selectedSummaries.reduce(
        (acc, summary) => acc + Number(summary.totalDeliveryCharges),
        0
      );
      const totalRevenue = selectedSummaries.reduce(
        (acc, summary) => acc + Number(summary.totalRevenue),
        0
      );
      // For child summaries, we need to calculate source price from their orders
      // This is an approximation since we don't have direct access to source prices in summaries
      const totalSourcePrice = totalRevenue - totalProfit;
      return {
        totalProfit,
        totalDeliveryCharges,
        totalRevenue,
        totalSourcePrice,
        combined: totalProfit + totalDeliveryCharges,
      };
    }
  };

  const finalizeSummary = async () => {
    if (!selectedType || !selectedDate) return;

    if (selectedItems.size === 0) {
      toast.error("Please select at least one item to include in the summary.");
      return;
    }

    setLoading(true);
    try {
      const { start, end } = getDateRange(selectedType, selectedDate);

      let totalOrders = 0;
      let totalRevenue = 0;
      let totalProfit = 0;
      let totalDeliveryCharges = 0;

      if (selectedType === SummaryType.DAILY) {
        const selectedOrders = orders.filter((order) =>
          selectedItems.has(order.id.toString())
        );
        totalOrders = selectedOrders.length;
        totalRevenue = selectedOrders.reduce(
          (acc, order) => acc + order.subTotal,
          0
        );
        totalDeliveryCharges = selectedOrders.reduce(
          (acc, order) => acc + order.shipping,
          0
        );
        totalProfit = selectedOrders.reduce(
          (acc, order) => acc + order.profit,
          0
        );
      } else {
        const selectedSummaries = childSummaries.filter((summary) =>
          selectedItems.has(summary.id)
        );
        totalOrders = selectedSummaries.reduce(
          (acc, summary) => acc + summary.totalOrders,
          0
        );
        totalRevenue = selectedSummaries.reduce(
          (acc, summary) => acc + Number(summary.totalRevenue),
          0
        );
        totalDeliveryCharges = selectedSummaries.reduce(
          (acc, summary) => acc + Number(summary.totalDeliveryCharges),
          0
        );
        totalProfit = selectedSummaries.reduce(
          (acc, summary) => acc + Number(summary.totalProfit),
          0
        );
      }

      await withError(
        createSummary({
          type: selectedType,
          startDate: start,
          endDate: end,
          totalOrders,
          totalRevenue,
          totalDeliveryCharges,
          totalProfit,
          notes: notes.trim() || null,
          orderIds:
            selectedType === SummaryType.DAILY
              ? orders
                  .filter((order) => selectedItems.has(order.id.toString()))
                  .map((order) => order.id)
              : [],
        })
      );

      toast.success("Summary created successfully!");
      onSummaryCreated();
      handleClose();
    } catch (error: any) {
      console.error("Failed to create summary:", error);
      toast.error(
        error?.message || "Failed to create summary. Please try again."
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto overflow-x-visible">
        <DialogHeader>
          <DialogTitle>Create New Summary</DialogTitle>
        </DialogHeader>

        {step === 1 && (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-4">
                Step 1: Select Summary Type & Date
              </h3>
              <div className="grid grid-cols-2 gap-4 mb-6">
                {summaryTypes.map((item) => (
                  <Card
                    key={item.type}
                    className={cn(
                      "cursor-pointer transition-colors hover:bg-gray-50",
                      selectedType === item.type &&
                        "ring-2 ring-blue-500 bg-blue-50"
                    )}
                    onClick={() => setSelectedType(item.type)}
                  >
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">{item.label}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-gray-600">
                        {item.description}
                      </p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>

            {selectedType && (
              <div>
                <h4 className="font-medium mb-2">Select Date</h4>
                <div className="space-y-3">
                  {/* Native date input - more reliable in modals */}
                  <div className="flex items-center space-x-2">
                    <CalendarIcon className="h-4 w-4 text-muted-foreground" />
                    <input
                      type="date"
                      value={
                        selectedDate ? format(selectedDate, "yyyy-MM-dd") : ""
                      }
                      onChange={(e) => {
                        const date = e.target.value
                          ? new Date(e.target.value)
                          : undefined;
                        setSelectedDate(date);
                      }}
                      className="flex h-10 w-[260px] rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    />
                  </div>

                  {selectedDate && (
                    <div className="text-sm text-muted-foreground">
                      Selected: {format(selectedDate, "EEEE, MMMM do, yyyy")}
                    </div>
                  )}
                </div>
              </div>
            )}

            <div className="flex justify-end">
              <Button
                onClick={fetchData}
                disabled={!selectedType || !selectedDate || loading}
                className="flex items-center gap-2"
              >
                {loading && <Loader2 className="h-4 w-4 animate-spin" />}
                Fetch Data
              </Button>
            </div>
          </div>
        )}

        {step === 2 && (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">
                Step 2: Data Selection & Adjustment
              </h3>
              <Badge variant="outline">
                {selectedType} - {selectedDate && format(selectedDate, "PPP")}
              </Badge>
            </div>

            {selectedType === SummaryType.DAILY && (
              <div>
                <div className="flex items-center justify-between mb-4">
                  <h4 className="font-medium">Orders for Selected Day</h4>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="select-all"
                      checked={
                        selectedItems.size === orders.length &&
                        orders.length > 0
                      }
                      onCheckedChange={handleSelectAll}
                    />
                    <label htmlFor="select-all" className="text-sm font-medium">
                      Select All
                    </label>
                  </div>
                </div>

                <div className="border rounded-lg">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-12"></TableHead>
                        <TableHead>Order ID</TableHead>
                        <TableHead>Customer</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">
                          Order Total
                        </TableHead>
                        <TableHead className="text-right">Charge</TableHead>
                        <TableHead className="text-right">Profit</TableHead>
                        <TableHead className="text-center">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {orders.map((order) => {
                        const orderTotal = order.subTotal + order.shipping;

                        return (
                          <TableRow key={order.id}>
                            <TableCell>
                              <Checkbox
                                checked={selectedItems.has(order.id.toString())}
                                onCheckedChange={(checked) =>
                                  handleItemSelect(
                                    order.id.toString(),
                                    checked as boolean
                                  )
                                }
                              />
                            </TableCell>
                            <TableCell className="font-medium">
                              <Link
                                href={`/dashboard/orders/${order.id}`}
                                target="_blank"
                              >
                                #{order.id}
                              </Link>
                            </TableCell>
                            <TableCell>{order.buyer.name}</TableCell>
                            <TableCell>
                              <Badge variant="outline">{order.status}</Badge>
                            </TableCell>
                            <TableCell className="text-right">
                              ৳{orderTotal.toFixed(2)}
                            </TableCell>
                            <TableCell className="text-right">
                              ৳{order.shipping.toFixed(2)}
                            </TableCell>
                            <TableCell className="text-right">
                              <Badge
                                variant={
                                  order.profit > 0 ? "default" : "destructive"
                                }
                              >
                                ৳{order.profit}
                              </Badge>
                            </TableCell>
                            <TableCell className="text-center">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleEditOrder(order)}
                                className="h-8 px-2 text-xs"
                              >
                                Edit Profit
                              </Button>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </div>

                {/* Totals for Daily Orders */}
                {selectedItems.size > 0 && (
                  <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                    <h5 className="font-medium mb-3">Selected Items Summary</h5>
                    <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
                      <div className="text-center">
                        <div className="text-gray-600">Total Revenue</div>
                        <div className="font-semibold text-lg">
                          ৳{calculateTotals().totalRevenue.toLocaleString()}
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-gray-600">Total Source Price</div>
                        <div className="font-semibold text-lg">
                          ৳{calculateTotals().totalSourcePrice.toLocaleString()}
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-gray-600">Total Profit</div>
                        <div className="font-semibold text-lg">
                          ৳{calculateTotals().totalProfit.toLocaleString()}
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-gray-600">Total Delivery Charges</div>
                        <div className="font-semibold text-lg">
                          ৳{calculateTotals().totalDeliveryCharges.toLocaleString()}
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-gray-600">Combined Total</div>
                        <div className="font-semibold text-lg text-green-600">
                          ৳{calculateTotals().combined.toLocaleString()}
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}

            {selectedType !== SummaryType.DAILY && (
              <div>
                <div className="flex items-center justify-between mb-4">
                  <h4 className="font-medium">Child Summaries</h4>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="select-all-summaries"
                      checked={
                        selectedItems.size === childSummaries.length &&
                        childSummaries.length > 0
                      }
                      onCheckedChange={handleSelectAll}
                    />
                    <label
                      htmlFor="select-all-summaries"
                      className="text-sm font-medium"
                    >
                      Select All
                    </label>
                  </div>
                </div>

                <div className="border rounded-lg">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-12"></TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead>Type</TableHead>
                        <TableHead className="text-right">Revenue</TableHead>
                        <TableHead className="text-right">Delivery Charge</TableHead>
                        <TableHead className="text-right">Profit</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {childSummaries.map((summary) => (
                        <TableRow key={summary.id}>
                          <TableCell>
                            <Checkbox
                              checked={selectedItems.has(summary.id)}
                              onCheckedChange={(checked) =>
                                handleItemSelect(summary.id, checked as boolean)
                              }
                            />
                          </TableCell>
                          <TableCell>
                            {format(new Date(summary.startDate), "PPP")}
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">{summary.type}</Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            ৳{Number(summary.totalRevenue).toLocaleString()}
                          </TableCell>
                          <TableCell className="text-right">
                            ৳{Number(summary.totalDeliveryCharges).toLocaleString()}
                          </TableCell>
                          <TableCell className="text-right">
                            ৳{Number(summary.totalProfit).toLocaleString()}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>

                {/* Totals for Child Summaries */}
                {selectedItems.size > 0 && (
                  <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                    <h5 className="font-medium mb-3">Selected Items Summary</h5>
                    <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
                      <div className="text-center">
                        <div className="text-gray-600">Total Revenue</div>
                        <div className="font-semibold text-lg">
                          ৳{calculateTotals().totalRevenue.toLocaleString()}
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-gray-600">Total Source Price</div>
                        <div className="font-semibold text-lg">
                          ৳{calculateTotals().totalSourcePrice.toLocaleString()}
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-gray-600">Total Profit</div>
                        <div className="font-semibold text-lg">
                          ৳{calculateTotals().totalProfit.toLocaleString()}
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-gray-600">Total Delivery Charges</div>
                        <div className="font-semibold text-lg">
                          ৳{calculateTotals().totalDeliveryCharges.toLocaleString()}
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-gray-600">Combined Total</div>
                        <div className="font-semibold text-lg text-green-600">
                          ৳{calculateTotals().combined.toLocaleString()}
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}

            <div>
              <h4 className="font-medium mb-2">Notes (Optional)</h4>
              <Textarea
                placeholder="Add any notes about this summary..."
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                rows={3}
              />
            </div>

            <div className="flex justify-between">
              <Button variant="outline" onClick={() => setStep(1)}>
                Back
              </Button>
              <Button
                onClick={finalizeSummary}
                disabled={selectedItems.size === 0 || loading}
                className="flex items-center gap-2"
              >
                {loading && <Loader2 className="h-4 w-4 animate-spin" />}
                Finalize Summary
              </Button>
            </div>
          </div>
        )}
      </DialogContent>

      {/* Simple Order Edit Modal */}
      <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Edit Order Profit</DialogTitle>
          </DialogHeader>
          {editingOrder && (
            <div className="space-y-4">
              <div>
                <Label htmlFor="order-id">Order ID</Label>
                <Input
                  id="order-id"
                  value={`#${editingOrder.id}`}
                  disabled
                  className="bg-gray-50"
                />
              </div>
              <div>
                <Label htmlFor="customer">Customer</Label>
                <Input
                  id="customer"
                  value={editingOrder.buyer.name}
                  disabled
                  className="bg-gray-50"
                />
              </div>
              <div>
                <Label htmlFor="current-profit">Current Profit</Label>
                <Input
                  id="current-profit"
                  type="number"
                  step="0.01"
                  value={editingOrder.profit}
                  onChange={(e) => {
                    const newProfit = parseFloat(e.target.value) || 0;
                    setEditingOrder({
                      ...editingOrder,
                      profit: newProfit,
                    });
                  }}
                />
              </div>
              <div className="flex justify-end space-x-2">
                <Button
                  variant="outline"
                  onClick={() => setIsEditModalOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  onClick={async () => {
                    if (editingOrder) {
                      await handleSaveEditedOrder(editingOrder);
                    }
                  }}
                  disabled={loading}
                >
                  {loading ? "Saving..." : "Save Changes"}
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </Dialog>
  );
}
