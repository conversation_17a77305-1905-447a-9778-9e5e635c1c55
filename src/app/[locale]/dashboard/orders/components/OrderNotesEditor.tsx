"use client";

import { useState } from "react";
import { Button } from "@udoy/components/ui/button";
import { Textarea } from "@udoy/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@udoy/components/ui/dialog";
import { Edit, Save, X } from "lucide-react";
import { updateOrderNotes } from "../actions";
import { withError } from "@udoy/utils/app-error";
import { toast } from "sonner";
import Locale from "@udoy/components/Locale/Client";

interface OrderNotesEditorProps {
  orderId: number;
  currentNotes: string | null;
}

export function OrderNotesEditor({ orderId, currentNotes }: OrderNotesEditorProps) {
  const [open, setOpen] = useState(false);
  const [notes, setNotes] = useState(currentNotes || "");
  const [isLoading, setIsLoading] = useState(false);

  const handleSave = async () => {
    setIsLoading(true);
    try {
      await withError(updateOrderNotes(orderId, notes));
      toast.success("Order notes updated successfully");
      setOpen(false);
    } catch (error: any) {
      toast.error(error?.message || "Failed to update order notes");
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setNotes(currentNotes || "");
    setOpen(false);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="h-8">
          <Edit className="h-3 w-3 mr-1" />
          <Locale bn="সম্পাদনা">Edit</Locale>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>
            <Locale bn="অর্ডার নোট সম্পাদনা">Edit Order Notes</Locale>
          </DialogTitle>
          <DialogDescription>
            <Locale bn="এই অর্ডারের জন্য অতিরিক্ত নোট যোগ করুন বা আপডেট করুন।">
              Add or update additional notes for this order.
            </Locale>
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">
              <Locale bn="নোট">Notes</Locale>
            </label>
            <Textarea
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Enter additional notes for this order..."
              className="min-h-[120px] resize-none"
              maxLength={500}
            />
            <div className="text-xs text-muted-foreground text-right">
              {notes.length}/500 characters
            </div>
          </div>
        </div>

        <DialogFooter className="gap-2">
          <Button
            variant="outline"
            onClick={handleCancel}
            disabled={isLoading}
          >
            <X className="h-4 w-4 mr-1" />
            <Locale bn="বাতিল">Cancel</Locale>
          </Button>
          <Button
            onClick={handleSave}
            disabled={isLoading}
          >
            <Save className="h-4 w-4 mr-1" />
            {isLoading ? (
              <Locale bn="সংরক্ষণ করা হচ্ছে...">Saving...</Locale>
            ) : (
              <Locale bn="সংরক্ষণ">Save</Locale>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
