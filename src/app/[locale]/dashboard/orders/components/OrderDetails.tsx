"use client";

import { Fragment, useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { format } from "date-fns";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  Printer,
  Minus,
  Trash,
  Plus,
  Check,
  Clock,
  Edit,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { OrderStatus, OrderTimeline } from "@prisma/client";

import { Button } from "@udoy/components/ui/button";
import { Badge } from "@udoy/components/ui/badge";
import { Separator } from "@udoy/components/ui/separator";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@udoy/components/ui/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@udoy/components/ui/card";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@udoy/components/ui/tabs";

import { cn } from "@udoy/utils/shadcn";
import { OrderFullInterface } from "@udoy/utils/types";
import { withError } from "@udoy/utils/app-error";
import {
  addOrderItemFromCart,
  deleteOrderItem,
  updateOrderItemQuantity,
  toggleOrderItemPicked,
  updateOrderItemSourcePrice,
  updateOrderItemSellingPrice,
  updateOrderProfit,
} from "../actions";
import { toast } from "sonner";
import { OrderTimeline as OrderTimelineComponent } from "../../components/order-timeline";
import { StatusUpdateForm } from "../../components/status-update-form";
import { QuantityEditModal } from "./QuantityEditModal";
import Locale from "@udoy/components/Locale/Client";
import { Input } from "@udoy/components/ui/input";
import { Checkbox } from "@udoy/components/ui/checkbox";
import { ExternalLink } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@udoy/components/ui/dialog";
import { useLocale } from "next-intl";
import { UnitUtil } from "@udoy/utils/product-unit";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@udoy/components/ui/alert-dialog";
import { OrderNotesEditor } from "./OrderNotesEditor";

// Helper function to format currency
function formatCurrency(amount: number) {
  return new Intl.NumberFormat("bn-BD", {
    style: "currency",
    currency: "BDT",
    minimumFractionDigits: 0,
  }).format(amount);
}

// Helper function to get status badge variant
function getStatusBadgeVariant(status: string) {
  switch (status) {
    case "DELIVERED":
      return "default";
    case "PROCESSING":
      return "secondary";
    case "PENDING":
      return "outline";
    case "CANCELLED":
      return "destructive";
    case "DELIVERY":
      return "warning";
    case "RETURNED":
      return "destructive";
    default:
      return "outline";
  }
}

// Helper function to format date
function formatDate(date: Date) {
  return format(date, "MMM dd, yyyy h:mm a");
}

function OrderStatusView({
  order,
  className,
}: {
  order: OrderFullInterface & { timeline: OrderTimeline[] };
  className?: string;
}) {
  return (
    <div className={cn("md:col-span-2", className)}>
      <Card className="md:col-span-2">
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>Order Status</CardTitle>
            <CardDescription>Current status and history</CardDescription>
          </div>
          <Badge
            variant={getStatusBadgeVariant(order.status) as any}
            className={cn(
              "text-sm py-1 px-3",
              order.status === OrderStatus.SHIPPING &&
                "bg-yellow-500 hover:bg-yellow-600"
            )}
          >
            {order.status.replace("_", " ")}
          </Badge>
        </CardHeader>
        <CardContent>
          <OrderTimelineComponent timeline={order.timeline} />
        </CardContent>
        <CardFooter className="flex justify-between border-t pt-6">
          <div className="text-sm text-muted-foreground">
            Update the order status
          </div>
          <StatusUpdateForm orderId={order.id} currentStatus={order.status} />
        </CardFooter>
      </Card>
    </div>
  );
}

export function OrderDetails({
  order,
}: {
  order: OrderFullInterface & { address: { zone: { name: string } } } & {
    timeline: OrderTimeline[];
  };
}) {
  const router = useRouter();
  const [quantityEditModal, setQuantityEditModal] = useState<{
    isOpen: boolean;
    orderItem: any;
  }>({
    isOpen: false,
    orderItem: null,
  });

  // State for price editing dialogs
  const [priceEditDialog, setPriceEditDialog] = useState<{
    isOpen: boolean;
    itemId: number | null;
    field: "sourcePrice" | "sellingPrice" | null;
    value: string;
    updateProductPrice: boolean;
    itemName: string;
  }>({
    isOpen: false,
    itemId: null,
    field: null,
    value: "",
    updateProductPrice: false,
    itemName: "",
  });

  const [currentProfit, setCurrentProfit] = useState(order.profit);

  // Calculation mode state
  const [calculationMode, setCalculationMode] = useState(false);
  const [selectedItems, setSelectedItems] = useState<Set<number>>(new Set());
  const [longPressTimer, setLongPressTimer] = useState<NodeJS.Timeout | null>(null);

  const locale = useLocale();

  // Calculation mode functions
  const handleLongPressStart = (itemId: number) => {
    if (!calculationMode) {
      const timer = setTimeout(() => {
        setCalculationMode(true);
        setSelectedItems(new Set([itemId]));
        setLongPressTimer(null); // Clear timer after activation
      }, 500); // 500ms long press
      setLongPressTimer(timer);
    }
  };

  const handleLongPressEnd = () => {
    if (longPressTimer) {
      clearTimeout(longPressTimer);
      setLongPressTimer(null);
    }
  };

  const handleItemClick = (itemId: number, event: React.MouseEvent) => {
    event.stopPropagation(); // Prevent event bubbling

    if (calculationMode) {
      const newSelected = new Set(selectedItems);
      if (newSelected.has(itemId)) {
        newSelected.delete(itemId);
      } else {
        newSelected.add(itemId);
      }
      setSelectedItems(newSelected);

      // Exit calculation mode if no items are selected
      if (newSelected.size === 0) {
        setCalculationMode(false);
      }
    }
  };

  const handleClickOutside = (event: React.MouseEvent) => {
    // Only close if clicking on the main container itself, not on child elements
    if (calculationMode && event.target === event.currentTarget) {
      setCalculationMode(false);
      setSelectedItems(new Set());
    }
  };

  const calculateSelectedTotals = () => {
    const selectedOrderItems = order.orderItems.filter(item => selectedItems.has(item.id));
    const totalSourcePrice = selectedOrderItems.reduce((acc, item) => acc + (item.sourcePrice * item.quantity), 0);
    const totalSellingPrice = selectedOrderItems.reduce((acc, item) => acc + (item.price * item.quantity), 0);
    const totalProfit = totalSellingPrice - totalSourcePrice;

    return {
      totalSourcePrice,
      totalSellingPrice,
      totalProfit,
      itemCount: selectedOrderItems.length
    };
  };

  // Handle quantity decrement
  const handleDecrement = async (itemId: number) => {
    try {
      const currentItem = order.orderItems.find((item) => item.id === itemId)!;
      const newQuantity = Math.max(0.01, currentItem.quantity - 1);

      await withError(
        updateOrderItemQuantity({
          itemId,
          orderId: order.id,
          quantity: newQuantity,
        })
      );
      toast.success("Item quantity updated");
    } catch (error: any) {
      toast.error(error?.message || "Failed to update item quantity");
    }
  };

  // Handle item deletion
  const handleDelete = async (itemId: number) => {
    try {
      await withError(deleteOrderItem({ itemId, orderId: order.id }));
      toast.success("Item deleted");
    } catch (error: any) {
      toast.error(error?.message || "Failed to delete item");
    }
  };

  // Handle quantity increment
  const handleIncrement = async (itemId: number) => {
    try {
      const currentItem = order.orderItems.find((item) => item.id === itemId)!;
      const newQuantity = currentItem.quantity + 1;

      await withError(
        updateOrderItemQuantity({
          itemId,
          orderId: order.id,
          quantity: newQuantity,
        })
      );
      toast.success("Item quantity updated");
    } catch (error: any) {
      toast.error(error?.message || "Failed to update item quantity");
    }
  };

  const handleAddFromCart = async () => {
    try {
      await withError(addOrderItemFromCart({ orderId: order.id }));
      toast.success("Items added from cart");
    } catch (error: any) {
      toast.error(error?.message || "Failed to add items from cart");
    }
  };

  const handleOpenQuantityModal = (orderItem: any) => {
    setQuantityEditModal({
      isOpen: true,
      orderItem,
    });
  };

  const handleCloseQuantityModal = () => {
    setQuantityEditModal({
      isOpen: false,
      orderItem: null,
    });
  };

  const handleQuantityUpdated = () => {
    // Refresh the page to show updated data
    router.refresh();
  };

  // Handle picked status toggle
  const handleTogglePicked = async (itemId: number) => {
    try {
      await withError(toggleOrderItemPicked({ itemId, orderId: order.id }));
      toast.success("Picked status updated");
    } catch (error: any) {
      toast.error(error?.message || "Failed to update picked status");
    }
  };

  // Handle price editing dialog open
  const openPriceEditDialog = (
    itemId: number,
    field: "sourcePrice" | "sellingPrice",
    currentValue: number,
    itemName: string
  ) => {
    setPriceEditDialog({
      isOpen: true,
      itemId,
      field,
      value: currentValue.toString(),
      updateProductPrice: false,
      itemName,
    });
  };

  // Handle price editing dialog close
  const closePriceEditDialog = () => {
    setPriceEditDialog({
      isOpen: false,
      itemId: null,
      field: null,
      value: "",
      updateProductPrice: false,
      itemName: "",
    });
  };

  // Handle price editing save
  const savePriceEdit = async () => {
    if (!priceEditDialog.itemId || !priceEditDialog.field) return;

    const value = parseFloat(priceEditDialog.value);
    if (isNaN(value) || value <= 0) {
      toast.error("Please enter a valid positive number");
      return;
    }

    try {
      switch (priceEditDialog.field) {
        case "sourcePrice":
          await withError(
            updateOrderItemSourcePrice({
              itemId: priceEditDialog.itemId,
              orderId: order.id,
              sourcePrice: value,
              updateProductPrice: priceEditDialog.updateProductPrice,
            })
          );
          break;
        case "sellingPrice":
          await withError(
            updateOrderItemSellingPrice({
              itemId: priceEditDialog.itemId,
              orderId: order.id,
              sellingPrice: value,
              updateProductPrice: priceEditDialog.updateProductPrice,
            })
          );
          break;
      }
      toast.success(`${priceEditDialog.field} updated successfully`);
      closePriceEditDialog();
    } catch (error: any) {
      toast.error(
        error?.message || `Failed to update ${priceEditDialog.field}`
      );
    }
  };

  return (
    <div
      className="flex flex-col gap-6 relative"
      onClick={handleClickOutside}
    >
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/dashboard/orders">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Order #{order.id}
            </h1>
            <p className="text-muted-foreground">
              Placed on {formatDate(order.createdAt)}
            </p>
          </div>
        </div>
        <div className="flex flex-wrap items-center gap-2">
          <Button asChild>
            <Link
              href={`/api/orders/${order.id}/invoice`}
              target="_blank"
              className="w-full"
            >
              <Printer className="mr-2 h-4 w-4" />
              Download Invoice
            </Link>
          </Button>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        <OrderStatusView className="hidden md:block" order={order} />
        <Card>
          <CardHeader>
            <CardTitle>Order Summary</CardTitle>
            <CardDescription>Order details and totals</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-1">
              <div className="flex justify-between">
                <span className="text-muted-foreground">
                  <Locale bn="সাব-টোটাল">Subtotal</Locale>
                </span>
                <span>{formatCurrency(order.subTotal)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">
                  <Locale bn="ডেলিভারি চার্জ">Shipping</Locale>
                </span>
                <span>{formatCurrency(order.shipping)}</span>
              </div>
            </div>
            <Separator />
            <div className="flex justify-between font-medium">
              <span>
                <Locale bn="মোট">Total</Locale>
              </span>
              <span>{formatCurrency(order.subTotal + order.shipping)}</span>
            </div>
            <Separator />
            <div className="grid gap-3">
              <div className="flex justify-between">
                <span className="text-muted-foreground">
                  <Locale bn="কেনা দাম">Cost Price</Locale>
                </span>
                <span
                  className={
                    order.profit >= 0
                      ? "text-green-600 font-medium"
                      : "text-red-600 font-medium"
                  }
                >
                  {formatCurrency(order.subTotal - order.profit)}
                </span>
              </div>

              <Dialog>
                <DialogTrigger>
                  <div className="flex justify-between -mt-2">
                    <span className="text-muted-foreground">
                      <Locale bn="লাভ">Profit</Locale>
                    </span>
                    <span
                      className={
                        order.profit >= 0
                          ? "text-green-600 font-medium"
                          : "text-red-600 font-medium"
                      }
                    >
                      {formatCurrency(order.profit)}
                    </span>
                  </div>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>
                      <Locale bn="লাভ আপডেট করুন">Edit Profit</Locale>
                    </DialogTitle>
                  </DialogHeader>
                  <div className="">
                    <Input
                      value={currentProfit}
                      onChange={(e) => setCurrentProfit(Number(e.target.value))}
                      type="number"
                      placeholder="profit..."
                    />
                  </div>
                  <DialogFooter>
                    <DialogTrigger asChild>
                      <Button type="button" variant="outline" className="mt-1">
                        <Locale bn="বাতিল করুন">Cancel</Locale>
                      </Button>
                    </DialogTrigger>
                    <DialogTrigger asChild>
                      <Button
                        type="submit"
                        className=""
                        onClick={() =>
                          updateOrderProfit({
                            orderId: order.id,
                            profit: currentProfit,
                          })
                        }
                      >
                        <Locale bn="আপডেট করুন">Update Profit</Locale>
                      </Button>
                    </DialogTrigger>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
            <Separator />
            <div className="space-y-1">
              <div className="text-sm font-medium">Payment Information</div>
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Method</span>
                <span>{"Cash On Delivery"}</span>
              </div>
              {/* {order.paymentId && (
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Transaction ID</span>
                  <span>{order.paymentId}</span>
                </div>
              )} */}
            </div>
            <Separator />
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse duration-700"></div>
                  <div className="text-sm font-semibold text-orange-700">
                    <Locale bn="অতিরিক্ত নোট">Order Notes</Locale>
                  </div>
                </div>
                <OrderNotesEditor orderId={order.id} currentNotes={order.notes} />
              </div>
              {order.notes ? (
                <div className="text-sm bg-orange-50 border border-orange-200 p-3 rounded-md font-medium text-orange-900">
                  {order.notes}
                </div>
              ) : (
                <div className="text-sm text-muted-foreground italic p-3 bg-muted/30 rounded-md">
                  <Locale bn="কোন অতিরিক্ত নোট নেই">No additional notes</Locale>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Order Details Tabs */}
      <Tabs defaultValue="items">
        <TabsList className="w-full">
          <TabsTrigger value="items" className="flex-1">
            Order Items
          </TabsTrigger>
          <TabsTrigger value="customer" className="flex-1">
            Customer
          </TabsTrigger>
          <TabsTrigger value="shipping" className="flex-1">
            Shipping
          </TabsTrigger>
        </TabsList>
        <TabsContent value="items" className="mt-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between">
                <CardTitle>Order Items</CardTitle>
                <Button size="sm" onClick={handleAddFromCart}>
                  Add From Cart
                </Button>
              </div>
              <CardDescription>Items included in this order</CardDescription>
            </CardHeader>
            <CardContent>
              {/* Desktop Table View */}
              <div className="hidden md:block">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-16">#</TableHead>
                      <TableHead>Product</TableHead>
                      <TableHead>Picked</TableHead>
                      <TableHead>Quantity</TableHead>
                      <TableHead className="text-right">Source Price</TableHead>
                      <TableHead className="text-right">
                        Selling Price
                      </TableHead>
                      <TableHead className="text-right">Profit</TableHead>
                      <TableHead className="text-right">Total</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {order.orderItems.map((item, index) => {
                      const profit =
                        (item.price - item.sourcePrice) * item.quantity;

                      return (
                        <TableRow
                          key={item.id}
                          className={cn(
                            "cursor-pointer transition-colors",
                            calculationMode && selectedItems.has(item.id) && "border-2 border-black bg-blue-50",
                            calculationMode && "hover:bg-gray-50"
                          )}
                          onMouseDown={(e) => {
                            e.preventDefault();
                            handleLongPressStart(item.id);
                          }}
                          onMouseUp={handleLongPressEnd}
                          onMouseLeave={handleLongPressEnd}
                          onTouchStart={(e) => {
                            e.preventDefault();
                            handleLongPressStart(item.id);
                          }}
                          onTouchEnd={handleLongPressEnd}
                          onClick={(e) => handleItemClick(item.id, e)}
                        >
                          {/* Index */}
                          <TableCell className="font-medium">
                            {index + 1}
                          </TableCell>

                          {/* Product */}
                          <TableCell>
                            <div className="flex items-center gap-3">
                              {item.product.images &&
                                item.product.images.length > 0 && (
                                  <button
                                    onClick={() =>
                                      router.push(
                                        `/dashboard/products/${item.productId}/edit`
                                      )
                                    }
                                    className="relative h-10 w-10 overflow-hidden rounded-md hover:opacity-80 transition-opacity"
                                  >
                                    <Image
                                      src={
                                        item.product.images[0].url ||
                                        "/placeholder.svg"
                                      }
                                      alt={item.product.name}
                                      fill
                                      className="object-cover"
                                    />
                                  </button>
                                )}
                              <div>
                                <button
                                  onClick={() =>
                                    router.push(
                                      `/dashboard/products/${item.productId}/edit`
                                    )
                                  }
                                  className="font-medium hover:underline text-left"
                                >
                                  <Locale
                                    en={item.product.name}
                                    bn={item.product.nam}
                                  />
                                </button>
                                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                                  <span>SKU: {item.productId.slice(-8)}</span>
                                  <ExternalLink className="h-3 w-3" />
                                </div>
                              </div>
                            </div>
                          </TableCell>

                          {/* Picked Status */}
                          <TableCell>
                            <div className="flex flex-col gap-1">
                              <div className="flex gap-2">
                                <Checkbox
                                  checked={item.picked}
                                  onCheckedChange={() =>
                                    handleTogglePicked(item.id)
                                  }
                                />
                                {item.picker && (
                                  <div className="font-medium">
                                    {item.picker.name}
                                  </div>
                                )}
                              </div>
                              {item.picked && item.pickedAt && (
                                <div className="text-xs text-muted-foreground flex gap-1">
                                  <div>
                                    {format(new Date(item.pickedAt), "MMM dd")},
                                  </div>
                                  <div>
                                    {format(new Date(item.pickedAt), "h:mm a")}
                                  </div>
                                </div>
                              )}
                            </div>
                          </TableCell>

                          {/* Quantity */}
                          <TableCell>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleOpenQuantityModal(item)}
                              className=" font-normal hover:bg-muted flex gap-2 items-center justify-center"
                            >
                              <span>
                                {item.quantity % 1 === 0
                                  ? item.quantity.toString()
                                  : item.quantity
                                      .toFixed(2)
                                      .replace(/\.?0+$/, "")}
                              </span>

                              <Edit className="h-3 w-3 -mt-1" />
                            </Button>
                          </TableCell>

                          {/* Source Price */}
                          <TableCell className="text-right">
                            <div className="flex items-center justify-end gap-1">
                              <span>{formatCurrency(item.sourcePrice)}</span>
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() =>
                                  openPriceEditDialog(
                                    item.id,
                                    "sourcePrice",
                                    item.sourcePrice,
                                    item.product.name
                                  )
                                }
                              >
                                <Edit className="h-3 w-3" />
                              </Button>
                            </div>
                          </TableCell>

                          {/* Selling Price */}
                          <TableCell className="text-right">
                            <div className="flex items-center justify-end gap-1">
                              <span>{formatCurrency(item.price)}</span>
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() =>
                                  openPriceEditDialog(
                                    item.id,
                                    "sellingPrice",
                                    item.price,
                                    item.product.name
                                  )
                                }
                              >
                                <Edit className="h-3 w-3" />
                              </Button>
                            </div>
                          </TableCell>

                          {/* Profit */}
                          <TableCell className="text-right">
                            <span
                              className={
                                profit >= 0 ? "text-green-600" : "text-red-600"
                              }
                            >
                              {formatCurrency(profit)}
                            </span>
                          </TableCell>

                          {/* Total */}
                          <TableCell className="text-right">
                            {formatCurrency(item.price * item.quantity)}
                          </TableCell>

                          {/* Actions */}
                          <TableCell className="text-right">
                            <div className="flex items-center justify-end space-x-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleDecrement(item.id)}
                                disabled={item.quantity <= 0.01}
                              >
                                <Minus className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleIncrement(item.id)}
                                disabled={item.quantity >= item.product.supply}
                              >
                                <Plus className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="destructive"
                                size="sm"
                                onClick={() => handleDelete(item.id)}
                              >
                                <Trash className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </div>

              {/* Mobile Card View */}
              <div className="md:hidden space-y-4">
                {order.orderItems.map((item) => {
                  const profit =
                    (item.price - item.sourcePrice) * item.quantity;

                  return (
                    <Card
                      key={item.id}
                      className={cn(
                        "relative cursor-pointer transition-colors",
                        calculationMode && selectedItems.has(item.id) && "border-2 border-black bg-blue-50",
                        calculationMode && "hover:bg-gray-50"
                      )}
                      onMouseDown={(e) => {
                        e.preventDefault();
                        handleLongPressStart(item.id);
                      }}
                      onMouseUp={handleLongPressEnd}
                      onMouseLeave={handleLongPressEnd}
                      onTouchStart={(e) => {
                        e.preventDefault();
                        handleLongPressStart(item.id);
                      }}
                      onTouchEnd={handleLongPressEnd}
                      onClick={(e) => handleItemClick(item.id, e)}
                    >
                      <CardContent className="p-4">
                        {/* Mobile Layout */}
                        <div className="space-y-3">
                          <div className="flex items-start gap-2">
                            {item.product.images && item.product.images[0] ? (
                              <Image
                                width={60}
                                height={60}
                                src={item.product.images[0].url}
                                alt={item.product.name}
                                className="w-10 h-10 rounded-md object-cover flex-shrink-0 overflow-clip"
                              />
                            ) : (
                              <div className="w-10 h-10 rounded-md bg-muted flex items-center justify-center flex-shrink-0">
                                <span className="text-xs text-muted-foreground">
                                  No
                                </span>
                              </div>
                            )}
                            <div className="flex-1 min-w-0">
                              <div className="flex justify-between">
                                <Link
                                  href={`/dashboard/products/${item.productId}/edit`}
                                >
                                  <h3 className="font-medium text-sm leading-tight mb-1">
                                    <Locale bn={item.product.nam}>
                                      {item.product.name}
                                    </Locale>
                                  </h3>
                                </Link>
                                <div className="flex items-center gap-2 mb-2">
                                  {item.picked ? (
                                    <Button
                                      variant="outline"
                                      size="icon"
                                      className=" w-6 h-6 text-xs absolute  right-3 top-3 bg-green-400/50 text-white  border-green-400/70"
                                      onClick={() =>
                                        handleTogglePicked(item.id)
                                      }
                                    >
                                      <Check className="w-4" />
                                    </Button>
                                  ) : (
                                    <Button
                                      variant="outline"
                                      size="icon"
                                      className=" w-6 h-6 text-xs absolute  right-3 top-3 bg-destructive text-destructive-foreground border-destructive/30"
                                      onClick={() =>
                                        handleTogglePicked(item.id)
                                      }
                                    >
                                      <Clock className="w-4" />
                                    </Button>
                                  )}
                                </div>
                              </div>
                              {item.picked && item.pickedAt && (
                                <div className="text-xs text-muted-foreground">
                                  {format(new Date(item.pickedAt), "MMM dd")}{" "}
                                  {format(new Date(item.pickedAt), "h:mm a")}
                                  {item.picker && ` • ${item.picker.name}`}
                                </div>
                              )}
                            </div>
                          </div>

                          <div className="grid grid-cols-2 gap-2 text-xs">
                            <div
                              className="flex gap-2 items-center"
                              onClick={() => handleOpenQuantityModal(item)}
                            >
                              <span className="text-muted-foreground">
                                <Locale bn="পরিমাণ">Qty</Locale>:
                              </span>
                              <span className="font-medium text-left hover:text-primary hover:underline cursor-pointer">
                                {UnitUtil.getAmountUnit(
                                  item.quantity * item.product.amount,
                                  item.product.unit,
                                  locale
                                )}
                              </span>
                            </div>
                            <button
                              className="flex gap-2 items-center"
                              onClick={() =>
                                openPriceEditDialog(
                                  item.id,
                                  "sourcePrice",
                                  item.sourcePrice,
                                  item.product.name
                                )
                              }
                            >
                              <span className="text-muted-foreground">
                                <Locale bn="কেনা">Source</Locale>:
                              </span>
                              <span className="font-medium text-left hover:text-primary hover:underline cursor-pointer">
                                {formatCurrency(item.sourcePrice)}
                              </span>
                            </button>
                            <button
                              className="flex gap-2 items-center"
                              onClick={() =>
                                openPriceEditDialog(
                                  item.id,
                                  "sellingPrice",
                                  item.price,
                                  item.product.name
                                )
                              }
                            >
                              <span className="text-muted-foreground">
                                <Locale bn="বিক্রি">Selling</Locale>:
                              </span>
                              <span className="font-medium text-left hover:text-primary hover:underline cursor-pointer">
                                {formatCurrency(item.price)}
                              </span>
                            </button>
                            <div className="flex gap-2 items-center">
                              <span className="text-muted-foreground">
                                <Locale bn="লাভ">Profit</Locale>:
                              </span>
                              <div
                                className={`font-medium ${
                                  profit >= 0
                                    ? "text-green-600"
                                    : "text-red-600"
                                }`}
                              >
                                {formatCurrency(profit)}
                              </div>
                            </div>
                          </div>

                          <Separator />

                          <div className="select-none">
                            <div className="flex items-center justify-between">
                              <span className="text-xs text-muted-foreground">
                                <Locale bn="মোট:">Total:</Locale>
                              </span>
                              <span className="font-semibold text-sm">
                                {formatCurrency(item.price * item.quantity)}
                              </span>
                            </div>
                            <div className="flex items-center justify-between">
                              <span className="text-xs text-muted-foreground">
                                <Locale bn="কেনা:">Source:</Locale>
                              </span>
                              <span className="font-semibold text-sm">
                                {formatCurrency(
                                  item.price * item.quantity - profit
                                )}
                              </span>
                            </div>

                            {/* Action buttons for mobile */}
                            <div className="flex flex-col gap-1 mt-3">
                              <AlertDialog>
                                <AlertDialogTrigger asChild>
                                  <Button
                                    size="sm"
                                    variant="destructive"
                                    className="min-h-7 px-2 text-xs flex-1"
                                  >
                                    <Trash className="w-3 h-3 mr-1 -mt-0.5" />
                                    Remove
                                  </Button>
                                </AlertDialogTrigger>
                                <AlertDialogContent>
                                  <AlertDialogHeader>
                                    <AlertDialogTitle>
                                      <Locale bn="অর্ডার থেকে পণ্যটি বাদ দেওয়ার ব্যাপারে আপনি নিশ্চিত?">
                                        Are you sure you want to remove this
                                        item?
                                      </Locale>
                                    </AlertDialogTitle>
                                  </AlertDialogHeader>
                                  <AlertDialogFooter>
                                    <AlertDialogCancel>
                                      <Locale bn="রেখে দিন">Cancel</Locale>
                                    </AlertDialogCancel>
                                    <AlertDialogAction
                                      onClick={() => handleDelete(item.id)}
                                    >
                                      <Locale bn="হ্যাঁ, বাদ দিন">
                                        Yes, remove
                                      </Locale>
                                    </AlertDialogAction>
                                  </AlertDialogFooter>
                                </AlertDialogContent>
                              </AlertDialog>
                              <div className="flex gap-1">
                                <Button
                                  size="sm"
                                  variant="outline"
                                  className="h-7 px-2 text-xs bg-transparent flex-1"
                                  onClick={() => handleDecrement(item.id)}
                                  disabled={item.quantity <= 0.01}
                                >
                                  <Minus className="w-3 h-3" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  className="h-7 px-2 text-xs bg-transparent flex-1"
                                  onClick={() => handleIncrement(item.id)}
                                  disabled={
                                    item.quantity >= item.product.supply
                                  }
                                >
                                  <Plus className="w-3 h-3" />
                                </Button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="customer" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Customer Information</CardTitle>
              <CardDescription>
                Details about the customer who placed this order
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <h4 className="mb-2 text-sm font-medium">
                    Contact Information
                  </h4>
                  <div className="rounded-md border p-4">
                    <p className="font-medium">{order.buyer.name}</p>
                    <p>{order.buyer.email}</p>
                    <p>{order.address.phone}</p>
                  </div>
                </div>
                <div>
                  <h4 className="mb-2 text-sm font-medium">Billing Address</h4>
                  <div className="rounded-md border p-4">
                    <p className="font-medium flex justify-between items-center">
                      <span>{order.address.name}</span>
                      <Badge>{order.address.zone.name}</Badge>
                    </p>
                    <p className="mt-2 bg-muted px-3 py-1.5 rounded">
                      {order.address.home}
                    </p>
                    <div className="flex gap-2 mt-2">
                      <Button asChild variant="outline">
                        <a
                          href={`tel:${order.address.phone}`}
                          className="w-full"
                        >
                          <Locale bn="কল করুন">Call</Locale>
                        </a>
                      </Button>
                      {order.address.location && (
                        <Button className="" asChild variant="outline">
                          <Link
                            target="__blank"
                            className="w-full"
                            href={`https://www.google.com/maps/search/${encodeURIComponent(
                              order.address.location
                            )}`}
                          >
                            <Locale bn="লোকেশন দেখুন">Navigate</Locale>
                          </Link>
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex justify-end">
                <Button
                  variant="outline"
                  onClick={() =>
                    router.push(`/dashboard/customers/${order.buyer.id}`)
                  }
                  className="w-full"
                >
                  View Customer Profile
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="shipping" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Shipping Information</CardTitle>
              <CardDescription>
                Delivery details and shipping address
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <h4 className="mb-2 text-sm font-medium">Shipping Address</h4>
                  <div className="rounded-md border p-4">
                    <p className="font-medium">{order.address.name}</p>
                    <p>{order.address.home}</p>
                    <p>{order.address.zone.name}</p>
                    <p>{order.address.phone}</p>
                    {order.address.location && (
                      <p className="text-muted-foreground">
                        {order.address.location}
                      </p>
                    )}
                  </div>
                </div>
                <div>
                  <h4 className="mb-2 text-sm font-medium">Shipping Method</h4>
                  <div className="rounded-md border p-4">
                    <p className="font-medium">Standard Shipping</p>
                    <p className="text-muted-foreground">
                      Estimated delivery: 2-3 business days
                    </p>
                    <p className="mt-2">{formatCurrency(order.shipping)}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <OrderStatusView className="md:hidden" order={order} />

      {/* Quantity Edit Modal */}
      {quantityEditModal.orderItem && (
        <QuantityEditModal
          isOpen={quantityEditModal.isOpen}
          onClose={handleCloseQuantityModal}
          orderItem={quantityEditModal.orderItem}
          orderId={order.id}
          onQuantityUpdated={handleQuantityUpdated}
        />
      )}

      {/* Price Edit Dialog */}
      <Dialog open={priceEditDialog.isOpen} onOpenChange={closePriceEditDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              <Locale
                bn={
                  priceEditDialog.field === "sourcePrice"
                    ? "কেনা দাম আপডেট করুন"
                    : "বিক্রির দাম আপডেট করুন"
                }
              >
                Edit{" "}
                {priceEditDialog.field === "sourcePrice"
                  ? "Source Price"
                  : "Selling Price"}
              </Locale>
            </DialogTitle>
            <DialogDescription>
              Update the{" "}
              {priceEditDialog.field === "sourcePrice"
                ? "source price"
                : "selling price"}{" "}
              for {priceEditDialog.itemName}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">
                {priceEditDialog.field === "sourcePrice" ? (
                  <Locale bn="কেনা দাম">Source Price</Locale>
                ) : (
                  <Locale bn="বিক্রি দাম">Selling Price</Locale>
                )}
              </label>
              <Input
                type="number"
                value={priceEditDialog.value}
                onChange={(e) =>
                  setPriceEditDialog((prev) => ({
                    ...prev,
                    value: e.target.value,
                  }))
                }
                className="mt-1"
                step="0.01"
                min="0"
                placeholder="Enter price"
              />
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="updateProduct"
                checked={priceEditDialog.updateProductPrice}
                onCheckedChange={(checked) =>
                  setPriceEditDialog((prev) => ({
                    ...prev,
                    updateProductPrice: !!checked,
                  }))
                }
              />
              <label htmlFor="updateProduct" className="text-sm">
                <Locale
                  bn={`পণ্যের ${
                    priceEditDialog.field === "sourcePrice" ? "ক্রয়" : "বিক্রয়"
                  } মূল্যও আপডেট করুন`}
                >
                  Also update the original product{" "}
                  {priceEditDialog.field === "sourcePrice"
                    ? "source price"
                    : "selling price"}
                </Locale>
              </label>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={closePriceEditDialog}
              className="mt-1 md:mt-0"
            >
              <Locale bn="বাতিল করুন">Cancel</Locale>
            </Button>
            <Button onClick={savePriceEdit}>
              <Locale bn="আপডেট করুন">Save Changes</Locale>
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Calculation Mode Summary Box */}
      {calculationMode && selectedItems.size > 0 && (
        <div className="fixed bottom-0 left-0 right-0 bg-white border-t-2 border-gray-200 shadow-lg p-4 z-50">
          <div className="max-w-4xl mx-auto">
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-semibold text-lg">
                <Locale bn="নির্বাচিত আইটেম সারসংক্ষেপ">Selected Items Summary</Locale>
              </h3>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setCalculationMode(false);
                  setSelectedItems(new Set());
                }}
              >
                <Locale bn="বন্ধ করুন">Close</Locale>
              </Button>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div className="text-center">
                <div className="text-gray-600">
                  <Locale bn="নির্বাচিত আইটেম">Selected Items</Locale>
                </div>
                <div className="font-semibold text-lg">
                  {calculateSelectedTotals().itemCount}
                </div>
              </div>
              <div className="text-center">
                <div className="text-gray-600">
                  <Locale bn="মোট কেনা দাম">Total Source Price</Locale>
                </div>
                <div className="font-semibold text-lg">
                  {formatCurrency(calculateSelectedTotals().totalSourcePrice)}
                </div>
              </div>
              <div className="text-center">
                <div className="text-gray-600">
                  <Locale bn="মোট বিক্রয় দাম">Total Selling Price</Locale>
                </div>
                <div className="font-semibold text-lg">
                  {formatCurrency(calculateSelectedTotals().totalSellingPrice)}
                </div>
              </div>
              <div className="text-center">
                <div className="text-gray-600">
                  <Locale bn="মোট লাভ">Total Profit</Locale>
                </div>
                <div className={cn(
                  "font-semibold text-lg",
                  calculateSelectedTotals().totalProfit >= 0 ? "text-green-600" : "text-red-600"
                )}>
                  {formatCurrency(calculateSelectedTotals().totalProfit)}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}


    </div>
  );
}
