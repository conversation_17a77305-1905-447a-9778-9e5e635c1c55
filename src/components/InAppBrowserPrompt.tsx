"use client";

import { useState, useEffect } from "react";
import {
  Card,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  CardHeader,
  CardTitle,
} from "@udoy/components/ui/card";
import { Globe, AlertTriangle } from "lucide-react";
import { useInAppBrowser } from "@udoy/hooks/useInAppBrowser";
import { Button } from "./ui/button";
import { cn } from "@udoy/utils/shadcn";
import Locale from "./Locale/Client";

const OpenInBrowserPrompt = () => {
  const { isFacebookBrowser } = useInAppBrowser();
  const [isVisible, setIsVisible] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const [redirectAttempts, setRedirectAttempts] = useState(0);

  // Automatic redirection logic
  useEffect(() => {
    if (isFacebookBrowser && redirectAttempts < 3) {
      // Try automatic redirection first
      const timer = setTimeout(() => {
        try {
          // Try multiple redirection methods
          const redirectUrl = "intent://udoymart.com#Intent;scheme=https;end";

          // Method 1: Direct window.open
          const newWindow = window.open(redirectUrl, "_blank");

          // Method 2: If window.open fails, try location.href
          if (
            !newWindow ||
            newWindow.closed ||
            typeof newWindow.closed === "undefined"
          ) {
            window.location.href = redirectUrl;
          }

          setRedirectAttempts((prev) => prev + 1);
        } catch (error) {
          console.log("Automatic redirect failed, showing manual prompt");
          setRedirectAttempts(3); // Stop auto-redirect attempts
        }
      }, 1000); // Wait 1 second before first redirect attempt

      return () => clearTimeout(timer);
    }
  }, [isFacebookBrowser, redirectAttempts]);

  // Show popup if auto-redirect fails or after attempts
  useEffect(() => {
    if (isFacebookBrowser && redirectAttempts >= 3) {
      setIsVisible(true);
      // Add entrance animation
      setTimeout(() => setIsAnimating(true), 100);
    }
  }, [isFacebookBrowser, redirectAttempts]);

  // Manual redirect function
  const handleOpenInBrowser = () => {
    try {
      const redirectUrl = "intent://udoymart.com#Intent;scheme=https;end";

      // Try multiple methods for better compatibility
      const newWindow = window.open(redirectUrl, "_blank");

      if (
        !newWindow ||
        newWindow.closed ||
        typeof newWindow.closed === "undefined"
      ) {
        // Fallback methods
        window.location.href = redirectUrl;
      }
    } catch (error) {
      // Final fallback - try to open in system browser
      window.location.href = "https://udoymart.com";
    }
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div className="fixed inset-0 z-[100] flex items-center justify-center bg-black/50 backdrop-blur-sm">
      <div className="mx-4 max-w-sm w-full">
        <Card
          className={cn(
            "shadow-2xl border-red-500/30 transition-all duration-500 transform bg-white",
            isAnimating ? "scale-100 opacity-100" : "scale-95 opacity-0"
          )}
        >
          <CardHeader className="pb-4 text-center">
            <div className="flex flex-col items-center gap-3">
              <div className="bg-red-500/10 p-3 rounded-full animate-pulse">
                <AlertTriangle className="h-8 w-8 text-red-500" />
              </div>
              <div>
                <CardTitle className="text-xl font-bold text-red-600">
                  <Locale bn="গুরুত্বপূর্ণ নোটিশ!">Important Notice!</Locale>
                </CardTitle>
                <CardDescription className="text-sm mt-2 text-gray-600">
                  <Locale bn="ফেসবুক অ্যাপের ভিতর থেকে অর্ডার করা যাবে না। অর্ডার করতে অবশ্যই নিচের বাটনে ক্লিক করে ব্রাউজারে ওপেন করুন।">
                    You cannot place orders from within Facebook app. Please
                    click the button below to open in your browser to place
                    orders.
                  </Locale>
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardFooter className="flex flex-col gap-3 pt-0">
            <Button
              onClick={handleOpenInBrowser}
              className="text-base h-12 w-full bg-green-600 hover:bg-green-700 font-semibold animate-bounce"
            >
              <Globe className="h-5 w-5 mr-2" />
              <Locale bn="ব্রাউজারে ওপেন করুন">Open in Browser</Locale>
            </Button>
            <p className="text-xs text-center text-gray-500">
              <Locale bn="এই পপআপ বন্ধ করা যাবে না। অর্ডার করতে অবশ্যই ব্রাউজারে ওপেন করুন।">
                This popup cannot be closed. You must open in browser to place
                orders.
              </Locale>
            </p>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
};

export default OpenInBrowserPrompt;
